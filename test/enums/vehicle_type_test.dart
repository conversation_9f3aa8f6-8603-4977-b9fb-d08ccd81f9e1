import 'package:flutter_test/flutter_test.dart';
import '../../lib/enums/vehicle_type.dart';

void main() {
  group('VehicleType Tests', () {
    test('VehicleType should have correct API values', () {
      expect(VehicleType.car.apiValue, 'CAR');
      expect(VehicleType.motorcycle.apiValue, 'MOTORCYCLE');
    });

    test('VehicleType should have correct lowercase values', () {
      expect(VehicleType.car.lowercaseValue, 'car');
      expect(VehicleType.motorcycle.lowercaseValue, 'motorcycle');
    });

    test('VehicleType should have all expected values', () {
      expect(VehicleType.values.length, 2);
      expect(VehicleType.values, contains(VehicleType.car));
      expect(VehicleType.values, contains(VehicleType.motorcycle));
    });

    test('VehicleType.fromString should work with API values', () {
      expect(VehicleType.fromString('CAR'), VehicleType.car);
      expect(VehicleType.fromString('MOTORCYCLE'), VehicleType.motorcycle);
      expect(VehicleType.fromString('INVALID'), null);
      expect(VehicleType.fromString(null), null);
    });

    test('VehicleType.fromString should work with Chinese display names for backward compatibility', () {
      expect(VehicleType.fromString('汽车'), VehicleType.car);
      expect(VehicleType.fromString('摩托车'), VehicleType.motorcycle);
      // Test backward compatibility with old types
      expect(VehicleType.fromString('轻型车辆'), VehicleType.car);
      expect(VehicleType.fromString('拖车'), VehicleType.car);
      expect(VehicleType.fromString('重型拖车'), VehicleType.car);
      expect(VehicleType.fromString('重型车辆'), VehicleType.car);
    });
  });
}
