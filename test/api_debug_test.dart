import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/services/browser_headers.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';

void main() {
  group('API调试测试', () {
    late ApiService apiService;

    setUpAll(() async {
      apiService = ApiService();
      await apiService.initialize();
    });

    test('检查请求头格式', () async {
      final headers = await BrowserHeaders.instance.getBrowserHeaders(
        referer: 'https://vplates.com.au/create/check-combination',
      );

      print('=== 生成的请求头 ===');
      headers.forEach((key, value) {
        print('$key: $value');
      });

      // 验证关键请求头
      expect(headers['Accept'], equals('*/*'));
      expect(headers['Accept-Encoding'], contains('gzip'));
      expect(headers['User-Agent'], contains('Chrome'));
      expect(headers['Sec-CH-UA'], contains('Google Chrome'));
      expect(headers['X-Requested-With'], equals('XMLHttpRequest'));
      expect(headers['X-NewRelic-ID'], equals('VQIPU1dVDhACUVRaDwgGUlI='));
    });

    test('检查时间戳格式', () {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      print('生成的时间戳: $timestamp');
      
      // 验证时间戳是13位数字（毫秒级）
      expect(timestamp.toString().length, equals(13));
      
      // 验证时间戳是合理的（2024年以后）
      expect(timestamp, greaterThan(1700000000000));
    });

    test('模拟完整请求流程', () async {
      print('=== 开始模拟完整请求流程 ===');
      
      // 创建测试车牌
      final testPlate = PlateNumber(
        number: 'GN8888',
        type: 'Personalised',
      );

      try {
        print('正在查询车牌: ${testPlate.number}');
        
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );

        print('查询完成！');
        print('结果: ${results.first.isAvailable}');
        print('查询时间: ${results.first.queryTime}');
        
        // 检查Cookie状态
        final cookieStats = await apiService.getCookieStats();
        print('Cookie统计: $cookieStats');

      } catch (e) {
        print('查询失败: $e');
        
        // 如果是DioException，打印详细信息
        if (e.toString().contains('DioException')) {
          print('这是一个网络请求错误，可能的原因：');
          print('1. 请求头不够真实');
          print('2. 缺少必要的Cookie');
          print('3. 时间戳格式问题');
          print('4. 服务器检测到机器人行为');
        }
        
        // 重新抛出异常以便测试框架处理
        rethrow;
      }
    });

    test('检查Cookie管理', () async {
      final cookieStats = await apiService.getCookieStats();
      print('当前Cookie统计: $cookieStats');
      
      // 应该至少有一些Cookie（从预热会话中获取）
      expect(cookieStats, isNotEmpty);
    });

    test('验证URL构建', () {
      final baseUrl = 'https://vplates.com.au/vplatesapi/checkcombo';
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final params = {
        'vehicleType': 'car',
        'combination': 'GN8888',
        'productType': 'Create',
        'isRestyle': false,
        '_': timestamp,
      };
      
      final queryString = params.entries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
      final fullUrl = '$baseUrl?$queryString';
      
      print('构建的完整URL: $fullUrl');
      
      // 验证URL格式
      expect(fullUrl, contains('vehicleType=car'));
      expect(fullUrl, contains('combination=GN8888'));
      expect(fullUrl, contains('productType=Create'));
      expect(fullUrl, contains('isRestyle=false'));
      expect(fullUrl, contains('_=$timestamp'));
    });

    tearDownAll(() async {
      // 清理测试数据
      await apiService.clearCookies();
    });
  });
}
