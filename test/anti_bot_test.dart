import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/services/cookie_manager.dart';
import 'package:myplates_vic/services/browser_headers.dart';
import 'package:myplates_vic/services/human_behavior.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';

void main() {
  group('反机器人检测功能测试', () {
    late ApiService apiService;

    setUpAll(() async {
      apiService = ApiService();
      await apiService.initialize();
    });

    test('Cookie管理器初始化测试', () async {
      await CookieManager.instance.initialize();
      expect(CookieManager.instance.cookieJar, isNotNull);
    });

    test('浏览器请求头生成测试', () async {
      final headers = await BrowserHeaders.instance.getBrowserHeaders(
        referer: 'https://vplates.com.au/create/check-combination',
      );

      expect(headers, isNotEmpty);
      expect(headers['User-Agent'], isNotNull);
      expect(headers['Accept'], isNotNull);
      expect(headers['Accept-Language'], isNotNull);
      expect(headers['Accept-Encoding'], isNotNull);
      expect(headers['Referer'], equals('https://vplates.com.au/create/check-combination'));
      expect(headers['X-Browser-Fingerprint'], isNotNull);

      print('生成的请求头:');
      headers.forEach((key, value) {
        print('  $key: $value');
      });
    });

    test('人性化行为延迟测试', () async {
      final stopwatch = Stopwatch()..start();
      
      await HumanBehavior.instance.executeWithDelay(
        () async {
          return 'test_result';
        },
        operationId: 'test_operation',
        minDelayMs: 500,
        maxDelayMs: 1000,
      );
      
      stopwatch.stop();
      
      expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(500));
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      
      print('延迟时间: ${stopwatch.elapsedMilliseconds}ms');
    });

    test('API服务初始化测试', () async {
      expect(apiService.isInitialized, isTrue);
      
      final cookieStats = await apiService.getCookieStats();
      expect(cookieStats, isA<Map<String, int>>());
      
      print('Cookie统计: $cookieStats');
    });

    test('单个车牌查询测试（模拟）', () async {
      // 创建测试车牌
      final testPlate = PlateNumber(
        number: 'TEST123',
        type: 'Personalised',
        queryTime: DateTime.now(),
      );

      try {
        // 注意：这个测试可能会因为网络问题或Cloudflare保护而失败
        // 在实际环境中，我们需要确保有有效的网络连接
        final result = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('进度: $completed/$total');
          },
        );

        expect(result, isNotEmpty);
        expect(result.first.number, equals('TEST123'));
        
        print('查询结果: ${result.first.isAvailable}');
      } catch (e) {
        print('查询失败（这在测试环境中是正常的）: $e');
        // 在测试环境中，网络请求失败是正常的
        expect(e, isA<Exception>());
      }
    });

    test('人性化行为统计测试', () async {
      // 执行几个操作
      for (int i = 0; i < 3; i++) {
        await HumanBehavior.instance.executeWithDelay(
          () async => 'operation_$i',
          operationId: 'test_batch_$i',
        );
      }

      final stats = apiService.getBehaviorStats();
      expect(stats, isNotEmpty);
      
      print('行为统计: $stats');
    });

    test('Cookie解析测试', () {
      final cookieString = 'session_id=abc123; user_pref=dark_mode; csrf_token=xyz789';
      final cookies = CookieManager.instance.parseCookieString(cookieString);
      
      expect(cookies, hasLength(3));
      expect(cookies[0].name, equals('session_id'));
      expect(cookies[0].value, equals('abc123'));
      expect(cookies[1].name, equals('user_pref'));
      expect(cookies[1].value, equals('dark_mode'));
      expect(cookies[2].name, equals('csrf_token'));
      expect(cookies[2].value, equals('xyz789'));
      
      // 测试转换回字符串
      final reconstructed = CookieManager.instance.cookiesToString(cookies);
      expect(reconstructed, equals('session_id=abc123; user_pref=dark_mode; csrf_token=xyz789'));
      
      print('解析的Cookie: ${cookies.map((c) => '${c.name}=${c.value}').join(', ')}');
    });

    test('浏览器指纹一致性测试', () async {
      final headers1 = await BrowserHeaders.instance.getBrowserHeaders();
      final headers2 = await BrowserHeaders.instance.getBrowserHeaders();
      
      // 指纹应该在短时间内保持一致
      expect(headers1['X-Browser-Fingerprint'], equals(headers2['X-Browser-Fingerprint']));
      
      print('浏览器指纹: ${headers1['X-Browser-Fingerprint']}');
    });

    test('速率限制测试', () async {
      final operationId = 'rate_limit_test';
      
      // 快速执行多个操作，应该触发速率限制
      final futures = <Future>[];
      for (int i = 0; i < 10; i++) {
        futures.add(
          HumanBehavior.instance.executeWithDelay(
            () async => 'operation_$i',
            operationId: operationId,
          ).catchError((e) {
            // 预期会有一些操作因为速率限制而失败
            return 'failed_$i';
          }),
        );
      }
      
      final results = await Future.wait(futures);
      
      // 应该有一些操作成功，一些失败
      final successCount = results.where((r) => !r.toString().startsWith('failed')).length;
      final failureCount = results.length - successCount;
      
      print('成功操作: $successCount, 失败操作: $failureCount');
      
      // 至少应该有一些操作被速率限制阻止
      expect(failureCount, greaterThan(0));
    });

    tearDownAll(() async {
      // 清理测试数据
      await apiService.clearCookies();
      apiService.resetBehaviorStats();
    });
  });

  group('集成测试', () {
    test('完整工作流程测试', () async {
      final apiService = ApiService();
      
      // 1. 初始化服务
      await apiService.initialize();
      expect(apiService.isInitialized, isTrue);
      
      // 2. 检查初始状态
      final initialStats = await apiService.getCookieStats();
      print('初始Cookie统计: $initialStats');
      
      // 3. 启用WebView回退（在实际应用中使用）
      apiService.enableWebViewFallback();
      expect(apiService.isWebViewFallbackEnabled, isTrue);
      
      // 4. 创建测试车牌
      final testPlates = <PlateNumber>[
        PlateNumber(number: 'ABC123', type: 'Personalised', queryTime: DateTime.now()),
        PlateNumber(number: 'XYZ789', type: 'Personalised', queryTime: DateTime.now()),
      ];
      
      // 5. 模拟查询（在测试环境中可能会失败，这是正常的）
      try {
        final results = await apiService.checkPlatesAvailability(
          plates: testPlates,
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );
        
        print('查询完成，结果数量: ${results.length}');
      } catch (e) {
        print('查询失败（测试环境中正常）: $e');
      }
      
      // 6. 检查最终状态
      final finalStats = await apiService.getCookieStats();
      final behaviorStats = apiService.getBehaviorStats();
      
      print('最终Cookie统计: $finalStats');
      print('行为统计: $behaviorStats');
      
      // 7. 清理
      await apiService.clearCookies();
      apiService.resetBehaviorStats();
      apiService.disableWebViewFallback();
    });
  });
}
