import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/search_record.dart';

void main() {
  group('SearchRecord Tests', () {
    test('SearchRecord should be created with all required fields including plateType and vehicleType', () {
      final now = DateTime.now();
      final record = SearchRecord(
        id: '123',
        plateNumber: 'ABC123',
        isAvailable: true,
        searchTime: now,
        plateType: 'Personalised',
        vehicleType: '轻型车辆',
      );

      expect(record.id, equals('123'));
      expect(record.plateNumber, equals('ABC123'));
      expect(record.isAvailable, equals(true));
      expect(record.searchTime, equals(now));
      expect(record.plateType, equals('Personalised'));
      expect(record.vehicleType, equals('轻型车辆'));
    });

    test('SearchRecord.fromJson should handle plateType and vehicleType fields', () {
      final json = {
        'id': '123',
        'plateNumber': 'ABC123',
        'isAvailable': true,
        'searchTime': '2023-01-01T00:00:00.000Z',
        'plateType': 'Custom',
        'vehicleType': '摩托车',
      };

      final record = SearchRecord.fromJson(json);

      expect(record.id, equals('123'));
      expect(record.plateNumber, equals('ABC123'));
      expect(record.isAvailable, equals(true));
      expect(record.plateType, equals('Custom'));
      expect(record.vehicleType, equals('摩托车'));
    });

    test('SearchRecord.fromJson should default to Custom and null when fields are missing', () {
      final json = {
        'id': '123',
        'plateNumber': 'ABC123',
        'isAvailable': true,
        'searchTime': '2023-01-01T00:00:00.000Z',
        // plateType and vehicleType are missing
      };

      final record = SearchRecord.fromJson(json);

      expect(record.plateType, equals('Custom'));
      expect(record.vehicleType, isNull);
    });

    test('SearchRecord.toJson should include plateType and vehicleType fields', () {
      final now = DateTime.now();
      final record = SearchRecord(
        id: '123',
        plateNumber: 'ABC123',
        isAvailable: true,
        searchTime: now,
        plateType: 'Personalised',
        vehicleType: '汽车',
      );

      final json = record.toJson();

      expect(json['id'], equals('123'));
      expect(json['plateNumber'], equals('ABC123'));
      expect(json['isAvailable'], equals(true));
      expect(json['searchTime'], equals(now.toIso8601String()));
      expect(json['plateType'], equals('Personalised'));
      expect(json['vehicleType'], equals('汽车'));
    });

    test('SearchRecord.copyWith should update plateType and vehicleType fields', () {
      final now = DateTime.now();
      final original = SearchRecord(
        id: '123',
        plateNumber: 'ABC123',
        isAvailable: true,
        searchTime: now,
        plateType: 'Custom',
        vehicleType: '汽车',
      );

      final updated = original.copyWith(
        plateType: 'Personalised',
        vehicleType: '摩托车',
      );

      expect(updated.id, equals('123'));
      expect(updated.plateNumber, equals('ABC123'));
      expect(updated.isAvailable, equals(true));
      expect(updated.searchTime, equals(now));
      expect(updated.plateType, equals('Personalised'));
      expect(updated.vehicleType, equals('摩托车'));
    });
  });
}
