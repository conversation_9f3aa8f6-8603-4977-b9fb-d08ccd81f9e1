import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';

void main() {
  group('简单API测试', () {
    test('测试GN8888车牌查询', () async {
      final apiService = ApiService();
      await apiService.initialize();

      print('=== 开始测试车牌查询 ===');
      
      final testPlate = PlateNumber(
        number: 'GN8888',
        type: 'Personalised',
      );

      try {
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );

        print('查询成功完成！');
        print('车牌号: ${results.first.number}');
        print('是否可用: ${results.first.isAvailable}');
        print('查询时间: ${results.first.queryTime}');
        
        // 检查Cookie状态
        final cookieStats = await apiService.getCookieStats();
        print('Cookie统计: $cookieStats');

        // 验证结果
        expect(results, isNotEmpty);
        expect(results.first.number, equals('GN8888'));
        expect(results.first.queryTime, isNotNull);
        
        // 如果查询成功，isAvailable应该是bool值而不是null
        if (results.first.isAvailable != null) {
          print('✅ API查询成功，获得了有效结果');
        } else {
          print('⚠️ API查询完成但结果为null，可能需要进一步调试');
        }

      } catch (e) {
        print('❌ 查询失败: $e');
        
        // 如果是403错误，说明还是被Cloudflare阻止了
        if (e.toString().contains('403')) {
          print('仍然收到403错误，可能需要：');
          print('1. 检查更多的请求头');
          print('2. 添加更多的Cookie');
          print('3. 使用WebView回退机制');
        }
        
        rethrow;
      }
    });

    test('测试已知可用车牌', () async {
      final apiService = ApiService();
      await apiService.initialize();

      print('=== 测试已知可用车牌 ===');
      
      // 使用一个更简单的车牌号进行测试
      final testPlate = PlateNumber(
        number: 'ABC123',
        type: 'Personalised',
      );

      try {
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );

        print('查询结果: ${results.first.isAvailable}');
        
        expect(results, isNotEmpty);
        expect(results.first.number, equals('ABC123'));

      } catch (e) {
        print('查询失败: $e');
        // 在测试中，失败是可以接受的
      }
    });
  });
}
