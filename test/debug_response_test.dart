import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/services/browser_headers.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';
import 'package:dio/dio.dart';

void main() {
  group('响应调试测试', () {
    late ApiService apiService;

    setUpAll(() async {
      apiService = ApiService();
      await apiService.initialize();
      apiService.enableWebViewFallback();
    });

    test('详细调试API响应', () async {
      print('=== 详细调试API响应 ===');
      
      final testPlate = PlateNumber(
        number: 'GN82DF',
        type: 'Personalised',
      );

      // 获取请求头
      final headers = await BrowserHeaders.instance.getBrowserHeaders(
        referer: 'https://vplates.com.au/create/check-combination',
      );
      
      print('=== 请求头信息 ===');
      headers.forEach((key, value) {
        print('$key: $value');
      });
      
      // 构建URL
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final url = 'https://vplates.com.au/vplatesapi/checkcombo';
      final params = {
        'vehicleType': VehicleType.car.apiValue,
        'combination': testPlate.number,
        'productType': 'Create',
        'isRestyle': false,
        '_': timestamp,
      };
      
      final queryString = params.entries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
      final fullUrl = '$url?$queryString';
      
      print('\n=== 请求信息 ===');
      print('完整URL: $fullUrl');
      print('时间戳: $timestamp');
      
      // 直接使用Dio进行请求以获取原始响应
      final dio = Dio();
      
      try {
        print('\n=== 发送请求 ===');
        final response = await dio.get(
          url,
          queryParameters: params,
          options: Options(
            headers: headers,
            validateStatus: (status) => true, // 接受所有状态码
            followRedirects: true,
            maxRedirects: 5,
          ),
        );
        
        print('\n=== 响应信息 ===');
        print('状态码: ${response.statusCode}');
        print('响应头:');
        response.headers.forEach((key, values) {
          print('  $key: ${values.join(', ')}');
        });
        
        print('\n=== 响应数据 ===');
        final data = response.data;
        print('数据类型: ${data.runtimeType}');
        
        if (data is String) {
          print('响应长度: ${data.length} 字符');
          
          // 检查是否是HTML
          if (data.toLowerCase().contains('<html')) {
            print('✅ 检测到HTML响应');
            
            // 检查Cloudflare挑战标识
            final indicators = [
              '_cf_chl_opt',
              'just a moment',
              'challenge-platform',
              'cloudflare',
              'checking your browser'
            ];
            
            print('\n=== Cloudflare挑战检测 ===');
            for (final indicator in indicators) {
              final found = data.toLowerCase().contains(indicator.toLowerCase());
              print('$indicator: ${found ? "✅ 找到" : "❌ 未找到"}');
            }
            
            // 显示HTML的前1000字符
            print('\n=== HTML响应内容（前1000字符）===');
            final preview = data.length > 1000 ? data.substring(0, 1000) : data;
            print(preview);
            print('...');
            
            // 尝试提取挑战信息
            if (data.contains('_cf_chl_opt')) {
              print('\n=== 提取挑战信息 ===');
              final regex = RegExp(r'window\._cf_chl_opt\s*=\s*\{([^}]+)\}');
              final match = regex.firstMatch(data);
              if (match != null) {
                print('挑战配置: ${match.group(0)}');
              }
            }
            
          } else {
            print('❌ 不是HTML响应');
            print('响应内容: $data');
          }
        } else if (data is Map) {
          print('✅ JSON响应');
          print('响应数据: $data');
        } else {
          print('❓ 未知响应格式');
          print('响应数据: $data');
        }
        
      } catch (e) {
        print('\n=== 请求异常 ===');
        print('异常类型: ${e.runtimeType}');
        print('异常信息: $e');
        
        if (e is DioException) {
          print('DioException详情:');
          print('  类型: ${e.type}');
          print('  消息: ${e.message}');
          print('  状态码: ${e.response?.statusCode}');
          
          if (e.response?.data != null) {
            final responseData = e.response!.data.toString();
            print('  响应数据长度: ${responseData.length}');
            
            if (responseData.length > 0) {
              final preview = responseData.length > 500 ? responseData.substring(0, 500) : responseData;
              print('  响应预览: $preview');
            }
          }
        }
      }
    });

    test('测试Cookie状态', () async {
      print('\n=== Cookie状态检查 ===');
      
      final cookieStats = await apiService.getCookieStats();
      print('Cookie统计: $cookieStats');
      
      // 检查行为统计
      final behaviorStats = apiService.getBehaviorStats();
      print('行为统计: $behaviorStats');
    });

    tearDownAll(() async {
      await apiService.clearCookies();
      apiService.disableWebViewFallback();
    });
  });
}
