import 'package:flutter_test/flutter_test.dart';
import 'package:myplates_vic/services/api_service.dart';
import 'package:myplates_vic/models/plate_number.dart';
import 'package:myplates_vic/enums/vehicle_type.dart';

void main() {
  group('Cloudflare挑战检测测试', () {
    late ApiService apiService;

    setUpAll(() async {
      apiService = ApiService();
      await apiService.initialize();
      // 启用WebView回退以处理Cloudflare挑战
      apiService.enableWebViewFallback();
    });

    test('验证Cloudflare挑战检测机制', () async {
      print('=== 验证Cloudflare挑战检测机制 ===');
      
      final testPlate = PlateNumber(
        number: 'GN82DF',
        type: 'Personalised',
      );

      print('开始查询车牌: ${testPlate.number}');
      print('WebView回退已启用: ${apiService.isWebViewFallbackEnabled}');
      
      bool cloudflareDetected = false;
      String detectionReason = '';
      
      try {
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('查询进度: $completed/$total');
          },
        );

        print('意外成功: ${results.first.isAvailable}');
        
      } catch (e) {
        print('捕获到异常: $e');
        
        // 检查是否是我们期望的Cloudflare挑战异常
        final errorMessage = e.toString().toLowerCase();
        
        if (errorMessage.contains('cloudflare') && errorMessage.contains('挑战')) {
          cloudflareDetected = true;
          detectionReason = 'API服务检测到Cloudflare挑战';
          print('✅ $detectionReason');
        } else if (errorMessage.contains('403')) {
          cloudflareDetected = true;
          detectionReason = '403状态码表明Cloudflare阻止';
          print('✅ $detectionReason');
        } else if (errorMessage.contains('html')) {
          cloudflareDetected = true;
          detectionReason = 'HTML响应表明可能是挑战页面';
          print('✅ $detectionReason');
        } else {
          print('❌ 意外的错误类型: $e');
        }
      }
      
      // 验证检测结果
      expect(cloudflareDetected, isTrue, reason: '应该检测到Cloudflare挑战');
      print('\n=== 检测结果 ===');
      print('Cloudflare挑战检测: ${cloudflareDetected ? "成功" : "失败"}');
      print('检测原因: $detectionReason');
      
      // 检查Cookie状态
      final cookieStats = await apiService.getCookieStats();
      print('Cookie统计: $cookieStats');
      
      // 验证Cookie中是否有Cloudflare相关的Cookie
      if (cookieStats['vplates.com.au'] != null && cookieStats['vplates.com.au']! > 0) {
        print('✅ 检测到vplates.com.au的Cookie');
      }
    });

    test('验证WebView回退机制配置', () {
      print('\n=== 验证WebView回退机制配置 ===');
      
      // 验证WebView回退已启用
      expect(apiService.isWebViewFallbackEnabled, isTrue);
      print('✅ WebView回退机制已启用');
      
      // 验证API服务已初始化
      expect(apiService.isInitialized, isTrue);
      print('✅ API服务已正确初始化');
      
      // 测试禁用和启用WebView回退
      apiService.disableWebViewFallback();
      expect(apiService.isWebViewFallbackEnabled, isFalse);
      print('✅ WebView回退机制可以正确禁用');
      
      apiService.enableWebViewFallback();
      expect(apiService.isWebViewFallbackEnabled, isTrue);
      print('✅ WebView回退机制可以正确启用');
    });

    test('验证请求参数和URL构建', () {
      print('\n=== 验证请求参数和URL构建 ===');
      
      // 验证vehicleType参数
      expect(VehicleType.car.apiValue, equals('CAR'));
      print('✅ VehicleType.car = ${VehicleType.car.apiValue}');
      
      // 验证时间戳格式
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      expect(timestamp.toString().length, equals(13));
      print('✅ 时间戳格式正确: $timestamp (${timestamp.toString().length}位)');
      
      // 验证URL构建
      final baseUrl = 'https://vplates.com.au/vplatesapi/checkcombo';
      final params = {
        'vehicleType': VehicleType.car.apiValue,
        'combination': 'GN82DF',
        'productType': 'Create',
        'isRestyle': false,
        '_': timestamp,
      };
      
      final queryString = params.entries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
      final fullUrl = '$baseUrl?$queryString';
      
      print('构建的URL: $fullUrl');
      
      // 验证URL包含所有必要参数
      expect(fullUrl, contains('vehicleType=CAR'));
      expect(fullUrl, contains('combination=GN82DF'));
      expect(fullUrl, contains('productType=Create'));
      expect(fullUrl, contains('isRestyle=false'));
      expect(fullUrl, contains('_=$timestamp'));
      
      print('✅ URL构建正确，包含所有必要参数');
    });

    tearDownAll(() async {
      // 清理测试数据
      await apiService.clearCookies();
      apiService.disableWebViewFallback();
      print('\n=== 测试清理完成 ===');
    });
  });

  group('实际使用场景模拟', () {
    test('模拟完整的Cloudflare挑战处理流程', () async {
      print('\n=== 模拟完整的Cloudflare挑战处理流程 ===');
      
      final apiService = ApiService();
      await apiService.initialize();
      apiService.enableWebViewFallback();
      
      final testPlate = PlateNumber(
        number: 'TEST123',
        type: 'Personalised',
      );
      
      print('步骤1: 尝试正常API请求');
      
      try {
        final results = await apiService.checkPlatesAvailability(
          plates: [testPlate],
          concurrency: 1,
          vehicleType: VehicleType.car,
          onProgress: (completed, total) {
            print('  查询进度: $completed/$total');
          },
        );
        
        print('步骤2: API请求成功完成');
        print('  结果: ${results.first.isAvailable}');
        
      } catch (e) {
        print('步骤2: API请求被Cloudflare阻止');
        print('  错误: $e');
        
        if (e.toString().contains('Cloudflare') || e.toString().contains('挑战')) {
          print('步骤3: 检测到Cloudflare挑战，应该启动WebView');
          print('  在实际应用中，此时会显示WebView验证页面');
          print('  用户完成验证后，Cookie会被更新');
          print('  然后可以重新尝试API请求');
          
          // 模拟验证成功后的状态
          print('步骤4: 模拟验证成功');
          print('  Cookie已更新，可以重新尝试API请求');
          
          expect(true, isTrue, reason: 'Cloudflare挑战检测和处理流程正常');
        } else {
          print('❌ 意外的错误，不是Cloudflare挑战');
          fail('应该检测到Cloudflare挑战');
        }
      }
      
      await apiService.clearCookies();
    });
  });
}
