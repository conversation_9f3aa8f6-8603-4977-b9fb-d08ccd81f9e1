# Publishing Your Flutter App

This document provides a guide on how to publish your Flutter application to the Apple App Store and Google Play Store.

## Google Play Store (Android)

### 1. Create an Upload Keystore

You need to sign your app with an upload key. To create one, run the following command in your terminal:

```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

This command will prompt you for a password for the keystore and key. Remember these passwords.

### 2. Configure Signing in Gradle

Create a file named `android/key.properties` with the following content:

```
storePassword=<keystore_password>
keyPassword=<key_password>
keyAlias=upload
storeFile=<path_to_your_keystore_file>
```

**Important:** Do not commit this file to your version control. Add `key.properties` to your `.gitignore` file.

Now, edit your `android/app/build.gradle.kts` file to use this configuration for release builds.

```kotlin
...
android {
    ...
    signingConfigs {
        create("release") {
            val keyProperties = java.util.Properties()
            val keyPropertiesFile = rootProject.file("key.properties")
            if (keyPropertiesFile.exists()) {
                keyProperties.load(java.io.FileInputStream(keyPropertiesFile))
            }

            keyAlias = keyProperties["keyAlias"] as String
            keyPassword = keyProperties["keyPassword"] as String
            storeFile = file(keyProperties["storeFile"] as String)
            storePassword = keyProperties["storePassword"] as String
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
        }
    }
}
...
```

### 3. Build the App Bundle

Run the following command to build the app bundle:

```bash
flutter build appbundle
```

The output will be at `build/app/outputs/bundle/release/app-release.aab`. You can now upload this file to the Google Play Console.

## Apple App Store (iOS)

### 1. Apple Developer Program

You need to be enrolled in the Apple Developer Program to publish apps to the App Store.

### 2. Xcode Configuration

Open your project in Xcode:

```bash
open ios/Runner.xcworkspace
```

In Xcode, select the `Runner` project in the project navigator. Then, in the main view, select the `Runner` target.

Go to the "Signing & Capabilities" tab.
- Check "Automatically manage signing".
- Select your team from the "Team" dropdown.

This will allow Xcode to automatically manage your app's provisioning profile and signing certificate.

### 3. Configure App Details

In the "General" tab, you should set the following:
- **Display Name:** The name of your app as it will appear on the App Store.
- **Bundle Identifier:** A unique identifier for your app.
- **Version:** The version number of your app.
- **Build:** The build number of your app.

### 4. Build and Archive

To build the app for release, run the following command:

```bash
flutter build ipa
```

This will create an `.ipa` file in the `build/ios/ipa/` directory.

Alternatively, you can create an archive from Xcode:
- Select "Product" > "Archive".
- Once the archive is created, the Organizer window will appear.
- From the Organizer, you can validate and distribute your app to the App Store.

### 5. App Store Connect

Log in to [App Store Connect](https://appstoreconnect.apple.com/) to create your app listing, fill in metadata, upload screenshots, and submit your app for review.
