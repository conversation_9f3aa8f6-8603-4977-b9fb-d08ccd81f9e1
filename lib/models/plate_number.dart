/// 车牌号码模型类
class PlateNumber {
  /// 车牌号码
  final String number;

  /// 车牌类型 (Personalised 或 Custom)
  final String type;

  /// 是否可用
  final bool? isAvailable;

  /// 查询时间戳
  final DateTime? queryTime;

  PlateNumber({
    required this.number,
    required this.type,
    this.isAvailable,
    this.queryTime,
  });

  /// 从JSON创建模型
  factory PlateNumber.fromJson(Map<String, dynamic> json) {
    return PlateNumber(
      number: json['number'] as String,
      type: json['type'] as String,
      isAvailable: json['isAvailable'] as bool?,
      queryTime: json['queryTime'] != null
          ? DateTime.parse(json['queryTime'] as String)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'type': type,
      'isAvailable': isAvailable,
      'queryTime': queryTime?.toIso8601String(),
    };
  }

  /// 创建副本并更新属性
  PlateNumber copyWith({
    String? number,
    String? type,
    bool? isAvailable,
    DateTime? queryTime,
  }) {
    return PlateNumber(
      number: number ?? this.number,
      type: type ?? this.type,
      isAvailable: isAvailable ?? this.isAvailable,
      queryTime: queryTime ?? this.queryTime,
    );
  }
}
