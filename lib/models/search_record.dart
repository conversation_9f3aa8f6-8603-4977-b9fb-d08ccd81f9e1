class SearchRecord {
  final String id;
  final String plateNumber;
  final bool isAvailable;
  final DateTime searchTime;
  final String plateType; // Added plate type field
  final String? vehicleType; // Added vehicle type field (nullable for backward compatibility)
  final bool isFavorite; // Added favorite field

  SearchRecord({
    required this.id,
    required this.plateNumber,
    required this.isAvailable,
    required this.searchTime,
    required this.plateType,
    this.vehicleType,
    this.isFavorite = false, // Default to false for backward compatibility
  });

  factory SearchRecord.fromJson(Map<String, dynamic> json) {
    return SearchRecord(
      id: json['id']?.toString() ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      plateNumber: json['plateNumber']?.toString() ?? '',
      isAvailable: json['isAvailable'] as bool? ?? false,
      searchTime: json['searchTime'] != null
          ? DateTime.parse(json['searchTime'] as String)
          : DateTime.now(),
      plateType: json['plateType']?.toString() ?? 'Custom', // Default to Custom if not specified
      vehicleType: json['vehicleType']?.toString(), // Can be null for backward compatibility
      isFavorite: json['isFavorite'] as bool? ?? false, // Default to false for backward compatibility
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'plateNumber': plateNumber,
      'isAvailable': isAvailable,
      'searchTime': searchTime.toIso8601String(),
      'plateType': plateType,
      'vehicleType': vehicleType,
      'isFavorite': isFavorite,
    };
  }

  SearchRecord copyWith({
    String? id,
    String? plateNumber,
    bool? isAvailable,
    DateTime? searchTime,
    String? plateType,
    String? vehicleType,
    bool? isFavorite,
  }) {
    return SearchRecord(
      id: id ?? this.id,
      plateNumber: plateNumber ?? this.plateNumber,
      isAvailable: isAvailable ?? this.isAvailable,
      searchTime: searchTime ?? this.searchTime,
      plateType: plateType ?? this.plateType,
      vehicleType: vehicleType ?? this.vehicleType,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}
