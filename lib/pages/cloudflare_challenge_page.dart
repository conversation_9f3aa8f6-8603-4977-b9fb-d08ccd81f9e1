import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/webview_service.dart';
import '../utils/logger.dart';

/// Cloudflare挑战处理页面
/// 用于显示WebView并处理Cloudflare验证
class CloudflareChallengePagePage extends StatefulWidget {
  final String challengeUrl;
  final Function(bool success)? onVerificationComplete;

  const CloudflareChallengePagePage({
    Key? key,
    required this.challengeUrl,
    this.onVerificationComplete,
  }) : super(key: key);

  @override
  State<CloudflareChallengePagePage> createState() => _CloudflareChallengePagePageState();
}

class _CloudflareChallengePagePageState extends State<CloudflareChallengePagePage> {
  late WebViewController _controller;
  bool _isLoading = true;
  String _statusMessage = '正在加载验证页面...';
  bool _verificationCompleted = false;
  bool _isVerifying = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewService.instance.createController(
      initialUrl: widget.challengeUrl,
      onPageStarted: (url) {
        if (mounted) {
          setState(() {
            _isLoading = true;
            _statusMessage = '正在加载页面...';
          });
        }
      },
      onPageFinished: (url) async {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }

        // 检查是否仍在Cloudflare挑战页面
        final hasChallenge = await WebViewService.instance.hasCloudflareChallenge();
        
        if (hasChallenge && !_isVerifying) {
          if (mounted) {
            setState(() {
              _statusMessage = '请完成Cloudflare验证';
              _isVerifying = true;
            });
          }
          
          // 开始等待验证完成
          _waitForVerification();
        } else if (!hasChallenge && !_verificationCompleted) {
          // 验证已完成
          _onVerificationSuccess();
        }
      },
      onWebResourceError: (error) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _statusMessage = '加载失败: ${error.description}';
          });
        }
      },
    );
  }

  void _waitForVerification() async {
    try {
      final success = await WebViewService.instance.waitForCloudflareVerification(
        timeout: const Duration(minutes: 5),
        onStatusUpdate: (status) {
          if (mounted) {
            setState(() {
              _statusMessage = status;
            });
          }
        },
      );

      if (success) {
        _onVerificationSuccess();
      } else {
        _onVerificationFailed();
      }
    } catch (e) {
      Logger.instance.error('等待Cloudflare验证时发生错误: $e', 'CloudflareChallengePagePage');
      _onVerificationFailed();
    }
  }

  void _onVerificationSuccess() {
    if (_verificationCompleted) return;
    
    if (mounted) {
      setState(() {
        _verificationCompleted = true;
        _statusMessage = '验证成功！正在返回...';
      });
    }

    Logger.instance.info('Cloudflare验证成功', 'CloudflareChallengePagePage');
    
    // 延迟返回
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop(true);
        widget.onVerificationComplete?.call(true);
      }
    });
  }

  void _onVerificationFailed() {
    if (_verificationCompleted) return;
    
    if (mounted) {
      setState(() {
        _verificationCompleted = true;
        _statusMessage = '验证失败或超时';
      });
    }

    Logger.instance.warning('Cloudflare验证失败', 'CloudflareChallengePagePage');
  }

  void _refresh() {
    _controller.reload();
    setState(() {
      _isLoading = true;
      _statusMessage = '正在刷新页面...';
      _verificationCompleted = false;
      _isVerifying = false;
    });
  }

  void _goBack() {
    _controller.goBack();
  }

  void _goForward() {
    _controller.goForward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cloudflare验证'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refresh,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 状态栏
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: _verificationCompleted 
              ? (_statusMessage.contains('成功') ? Colors.green[100] : Colors.red[100])
              : Colors.blue[100],
            child: Row(
              children: [
                if (_isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                if (_isLoading) const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _statusMessage,
                    style: TextStyle(
                      color: _verificationCompleted 
                        ? (_statusMessage.contains('成功') ? Colors.green[800] : Colors.red[800])
                        : Colors.blue[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 工具栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: _goBack,
                  tooltip: '后退',
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_forward),
                  onPressed: _goForward,
                  tooltip: '前进',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _refresh,
                  tooltip: '刷新',
                ),
                const Spacer(),
                if (_verificationCompleted && !_statusMessage.contains('成功'))
                  ElevatedButton(
                    onPressed: _refresh,
                    child: const Text('重试'),
                  ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                    widget.onVerificationComplete?.call(false);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                  ),
                  child: const Text('取消', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
          
          // WebView
          Expanded(
            child: WebViewWidget(controller: _controller),
          ),
          
          // 底部提示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '请完成页面中的验证步骤：',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '• 等待页面加载完成\n• 如果出现验证码，请按提示完成\n• 验证成功后会自动返回',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示Cloudflare验证页面的便捷方法
Future<bool> showCloudflareChallengePage(
  BuildContext context, {
  required String challengeUrl,
}) async {
  final result = await Navigator.of(context).push<bool>(
    MaterialPageRoute(
      builder: (context) => CloudflareChallengePagePage(
        challengeUrl: challengeUrl,
      ),
    ),
  );
  
  return result ?? false;
}
