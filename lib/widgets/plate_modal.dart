import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/plate_generator.dart';
import '../models/plate_number.dart';
import '../services/database_service.dart';
import '../enums/keyword_mode.dart';
import '../enums/vehicle_type.dart';
import '../l10n/app_localizations.dart';
import '../utils/logger.dart';
import '../utils/notification_manager.dart';

class PlateModal extends StatefulWidget {
  final String keyword;
  final int digits;
  final KeywordMode mode;
  final VehicleType vehicleType;

  const PlateModal({
    super.key,
    required this.keyword,
    required this.digits,
    required this.mode,
    required this.vehicleType,
  });

  @override
  State<PlateModal> createState() => _PlateModalState();
}

// 配置常量
class PlateModalConfig {
  // static int maxSelectionCount = 100; 
  static int get maxSelectionCount => kDebugMode ? 500 : 100; // Debug模式下500，Release模式下100
  static const int maxGenerateLimit = 1000; // 最大生成数量

  // Grid layout - 参考 history 页面设计
  static const double cardAspectRatio = 2.2; // 卡片宽高比（增加高度）
  static const double cardSpacing = 4.0; // 卡片间距
  static const double cardMinWidth = 100.0; // 最小卡片宽度

  // 计算每页显示数量（基于屏幕尺寸）
  static int getItemsPerPage(double screenWidth, double screenHeight) {
    final columns = getCardsPerRow(screenWidth);

    // 估算可用高度（减去标题、过滤器、统计条、分页控制等）
    final availableHeight = screenHeight * 0.85 - 400; // 更保守的估计

    // 估算每个卡片的高度（包括间距）
    final cardWidth = (screenWidth - 32 - (columns - 1) * cardSpacing) / columns;
    final cardHeight = cardWidth / cardAspectRatio;
    final itemHeightWithSpacing = cardHeight + cardSpacing;

    // 计算可以显示的行数（向下取整）
    final maxRows = (availableHeight / itemHeightWithSpacing).floor();
    final rows = maxRows > 0 ? maxRows : 1;

    // 确保是列数的倍数
    final itemsPerPage = rows * columns;
    return itemsPerPage >= columns ? itemsPerPage : columns;
  }

  // 计算每行卡片数量
  static int getCardsPerRow(double screenWidth) {
    if (screenWidth < 300) {
      return 2; // 超小屏幕: 2列
    } else if (screenWidth < 400) {
      return 3; // 小屏幕: 3列
    } else if (screenWidth < 500) {
      return 4; // 中等屏幕: 4列
    } else if (screenWidth < 600) {
      return 5; // 大屏幕: 5列
    } else {
      return 6; // 超大屏幕: 6列
    }
  }
}

// 过滤选项枚举
enum FilterOption {
  all,
  hasHistory,
  noHistory,
  available,
  unavailable,
  personalised,
  custom;

  String getLabel(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case FilterOption.all:
        return localizations.historyAll;
      case FilterOption.hasHistory:
        return localizations.plateModalHasHistory;
      case FilterOption.noHistory:
        return localizations.plateModalNoHistory;
      case FilterOption.available:
        return localizations.available;
      case FilterOption.unavailable:
        return localizations.unavailable;
      case FilterOption.personalised:
        return localizations.personalised;
      case FilterOption.custom:
        return localizations.custom;
    }
  }
}

// 排序选项枚举
enum SortOption {
  noHistoryFirst,
  unavailableFirst,
  availableFirst,
  alphabetical;

  String getLabel(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case SortOption.noHistoryFirst:
        return localizations.plateModalNoHistoryFirst;
      case SortOption.unavailableFirst:
        return localizations.historyUnavailableFirst;
      case SortOption.availableFirst:
        return localizations.historyAvailableFirst;
      case SortOption.alphabetical:
        return localizations.plateModalAlphabetical;
    }
  }
}

class _PlateModalState extends State<PlateModal> {
  List<PlateNumber> _allPlates = []; // 所有生成的车牌
  List<PlateNumber> _filteredPlates = []; // 过滤后的车牌
  List<PlateNumber> _currentPagePlates = []; // 当前页显示的车牌
  bool _isLoading = true;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  final DatabaseService _dbService = DatabaseService();
  Map<String, bool> _plateHistory = {};

  // 分页相关
  int _currentPage = 0;
  int _itemsPerPage = 20; // 将在 initState 中根据屏幕尺寸更新
  int get _totalPages => (_filteredPlates.length / _itemsPerPage).ceil();

  // 选择相关
  final Set<String> _selectedPlates = {};

  // 过滤和排序
  FilterOption _currentFilter = FilterOption.all;
  SortOption _currentSort = SortOption.noHistoryFirst;

  // 车牌格式规则
  final List<Map<String, dynamic>> _platePatterns = [
    {
      'pattern': RegExp(r'^[A-Z]{2}\d{2}[A-Z]{2}$'),
      'format': (String plate) =>
          '${plate.substring(0, 2)}.${plate.substring(2, 4)}.${plate.substring(4)}',
    },
    {
      'pattern': RegExp(r'^[A-Z]{2}\d{3,4}$'),
      'format': (String plate) =>
          '${plate.substring(0, 2)}.${plate.substring(2)}',
    },
    {
      'pattern': RegExp(r'^[A-Z]{3}\d{3}$'),
      'format': (String plate) =>
          '${plate.substring(0, 3)}.${plate.substring(3)}',
    },
    {
      'pattern': RegExp(r'^[A-Z]{3}\d{2}[A-Z]$'),
      'format': (String plate) =>
          '${plate.substring(0, 3)}.${plate.substring(3, 5)}.${plate.substring(5)}',
    },
    {
      'pattern': RegExp(r'^\d{2}[A-Z]{3,4}$'),
      'format': (String plate) =>
          '${plate.substring(0, 2)}.${plate.substring(2)}',
    },
    {
      'pattern': RegExp(r'^\d{3}[A-Z]{3}$'),
      'format': (String plate) =>
          '${plate.substring(0, 3)}.${plate.substring(3)}',
    },
  ];

  String _formatPlateNumber(String plate) {
    for (var pattern in _platePatterns) {
      if (pattern['pattern'].hasMatch(plate)) {
        return pattern['format'](plate);
      }
    }
    return plate;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 根据屏幕尺寸更新每页显示数量
      final size = MediaQuery.of(context).size;
      _itemsPerPage = PlateModalConfig.getItemsPerPage(size.width, size.height);
      _loadData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      // 生成车牌组合，使用更大的限制
      final List<String> plateNumbers = PlateGenerator.generatePlates(
        keyword: widget.keyword.trim(),
        digits: widget.digits,
        mode: widget.mode,
        limit: PlateModalConfig.maxGenerateLimit,
      );

      // 转换为PlateNumber对象
      _allPlates = plateNumbers
          .map((number) => PlateNumber(
                number: number,
                type: PlateGenerator.getPlateType(number),
                isAvailable: null,
                queryTime: null,
              ))
          .toList();

      // 查询数据库中的历史记录
      final records = await _dbService.getAllRecords();
      _plateHistory = {
        for (var record in records) record.number: record.isAvailable ?? false
      };

      // 应用初始过滤和排序
      _applyFilterAndSort();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      Logger.instance.error('加载数据时发生错误: $e', 'PlateModal');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
        _allPlates = [];
        _filteredPlates = [];
        _currentPagePlates = [];
      });
    }
  }

  // 应用过滤和排序
  void _applyFilterAndSort() {
    // 先过滤
    _filteredPlates = _allPlates.where((plate) {
      final hasHistory = _plateHistory.containsKey(plate.number);
      final isAvailable = _plateHistory[plate.number];

      switch (_currentFilter) {
        case FilterOption.all:
          return true;
        case FilterOption.hasHistory:
          return hasHistory;
        case FilterOption.noHistory:
          return !hasHistory;
        case FilterOption.available:
          return hasHistory && isAvailable == true;
        case FilterOption.unavailable:
          return hasHistory && isAvailable == false;
        case FilterOption.personalised:
          return plate.type == 'Personalised';
        case FilterOption.custom:
          return plate.type == 'Custom';
      }
    }).toList();

    // 再排序
    _filteredPlates.sort((a, b) {
      final aHasHistory = _plateHistory.containsKey(a.number);
      final bHasHistory = _plateHistory.containsKey(b.number);
      final aIsAvailable = _plateHistory[a.number];
      final bIsAvailable = _plateHistory[b.number];

      switch (_currentSort) {
        case SortOption.noHistoryFirst:
          if (aHasHistory != bHasHistory) {
            return aHasHistory ? 1 : -1; // 无历史优先
          }
          break;
        case SortOption.unavailableFirst:
          if (aHasHistory && bHasHistory) {
            if (aIsAvailable != bIsAvailable) {
              return (aIsAvailable == false) ? -1 : 1; // 不可用优先
            }
          }
          break;
        case SortOption.availableFirst:
          if (aHasHistory && bHasHistory) {
            if (aIsAvailable != bIsAvailable) {
              return (aIsAvailable == true) ? -1 : 1; // 可用优先
            }
          }
          break;
        case SortOption.alphabetical:
          return a.number.compareTo(b.number);
      }

      // 默认按字母顺序排序
      return a.number.compareTo(b.number);
    });

    // 重置到第一页
    _currentPage = 0;
    _updateCurrentPagePlates();
  }

  // 更新当前页显示的车牌
  void _updateCurrentPagePlates() {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex = (startIndex + _itemsPerPage)
        .clamp(0, _filteredPlates.length);

    _currentPagePlates = _filteredPlates.sublist(startIndex, endIndex);
  }

  // 切换页面
  void _goToPage(int page) {
    if (page >= 0 && page < _totalPages) {
      setState(() {
        _currentPage = page;
        _updateCurrentPagePlates();
      });
    }
  }

  // 选择/取消选择车牌
  void _togglePlateSelection(String plateNumber) {
    setState(() {
      if (_selectedPlates.contains(plateNumber)) {
        _selectedPlates.remove(plateNumber);
      } else {
        if (_selectedPlates.length < PlateModalConfig.maxSelectionCount) {
          _selectedPlates.add(plateNumber);
        } else {
          final localizations = AppLocalizations.of(context);
          NotificationManager.instance.showWarning(
            context,
            localizations.plateModalMaxSelection.replaceAll('{}', '${PlateModalConfig.maxSelectionCount}'),
          );
        }
      }
    });
  }

  // 批量选择功能
  void _selectCurrentPage() {
    setState(() {
      for (final plate in _currentPagePlates) {
        if (_selectedPlates.length < PlateModalConfig.maxSelectionCount) {
          _selectedPlates.add(plate.number);
        } else {
          break;
        }
      }
    });
  }

  void _selectAllNoHistory() {
    setState(() {
      final noHistoryPlates = _filteredPlates
          .where((plate) => !_plateHistory.containsKey(plate.number))
          .take(PlateModalConfig.maxSelectionCount - _selectedPlates.length);

      for (final plate in noHistoryPlates) {
        _selectedPlates.add(plate.number);
      }
    });
  }

  void _selectAllUnavailable() {
    setState(() {
      final unavailablePlates = _filteredPlates
          .where((plate) => _plateHistory.containsKey(plate.number) &&
                           _plateHistory[plate.number] == false)
          .take(PlateModalConfig.maxSelectionCount - _selectedPlates.length);

      for (final plate in unavailablePlates) {
        _selectedPlates.add(plate.number);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedPlates.clear();
    });
  }

  // 构建紧凑按钮（带文字）
  Widget _buildCompactButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 14),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.lightBlue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          textStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
          minimumSize: const Size(0, 32),
          elevation: 2,
        ),
      ),
    );
  }

  // 构建紧凑图标按钮（仅图标）
  Widget _buildCompactIconButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.lightBlue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(6),
          minimumSize: const Size(32, 32),
          elevation: 2,
        ),
        child: Icon(icon, size: 14),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFE3F2FD), // 浅天蓝色
            Color(0xFF1976D2), // 深天蓝色
          ],
        ),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Container(
        margin: const EdgeInsets.only(top: 2),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
        ),
        padding: const EdgeInsets.all(16),
        height: MediaQuery.of(context).size.height * 0.85,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.plateModalTitle,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.lightBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      color: Colors.grey[600],
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 过滤和排序控件
            if (!_isLoading && _errorMessage == null && _allPlates.isNotEmpty) ...[
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<FilterOption>(
                      value: _currentFilter,
                      isExpanded: true,
                      decoration: InputDecoration(
                        labelText: localizations.plateModalFilter,
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: FilterOption.values.map((filter) {
                        return DropdownMenuItem(
                          value: filter,
                          child: Text(
                            filter.getLabel(context),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _currentFilter = value;
                            _applyFilterAndSort();
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButtonFormField<SortOption>(
                      value: _currentSort,
                      isExpanded: true,
                      decoration: InputDecoration(
                        labelText: localizations.plateModalSort,
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: SortOption.values.map((sort) {
                        return DropdownMenuItem(
                          value: sort,
                          child: Text(
                            sort.getLabel(context),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _currentSort = value;
                            _applyFilterAndSort();
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 批量选择控件 - 优化为单行布局
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildCompactButton(
                      onPressed: _currentPagePlates.isEmpty ? null : _selectCurrentPage,
                      icon: Icons.select_all,
                      label: localizations.plateModalCurrentPage,
                      tooltip: localizations.plateModalCurrentPage,
                    ),
                    const SizedBox(width: 6),
                    _buildCompactButton(
                      onPressed: _selectAllNoHistory,
                      icon: Icons.new_releases,
                      label: localizations.plateModalNoHistory,
                      tooltip: localizations.plateModalNoHistory,
                    ),
                    const SizedBox(width: 6),
                    _buildCompactButton(
                      onPressed: _selectAllUnavailable,
                      icon: Icons.block,
                      label: localizations.plateModalUnavailable,
                      tooltip: localizations.plateModalUnavailable,
                    ),
                    const SizedBox(width: 6),
                    _buildCompactIconButton(
                      onPressed: _selectedPlates.isEmpty ? null : _clearSelection,
                      icon: Icons.clear,
                      tooltip: localizations.plateModalClearSelection,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // 统计信息
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${localizations.plateModalTotal}: ${_allPlates.length} | ${localizations.plateModalFiltered}: ${_filteredPlates.length}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      localizations.plateModalSelected.replaceAll('{}', '${_selectedPlates.length}/${PlateModalConfig.maxSelectionCount}'),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _selectedPlates.length >= PlateModalConfig.maxSelectionCount
                            ? Colors.red : null,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_errorMessage != null)
              Expanded(
                child: Center(
                  child: Text(
                    '${localizations.plateModalGenerationFailed}: $_errorMessage',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              )
            else if (_filteredPlates.isEmpty)
              Expanded(
                child: Center(
                  child: Text(localizations.plateModalNoPlatesWithFilter),
                ),
              )
            else ...[
              // 车牌网格
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: GridView.builder(
                    controller: _scrollController,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: PlateModalConfig.getCardsPerRow(MediaQuery.of(context).size.width),
                      childAspectRatio: PlateModalConfig.cardAspectRatio,
                      crossAxisSpacing: PlateModalConfig.cardSpacing,
                      mainAxisSpacing: PlateModalConfig.cardSpacing,
                    ),
                  itemCount: _currentPagePlates.length,
                  itemBuilder: (context, index) {
                    final plate = _currentPagePlates[index];
                    final hasHistory = _plateHistory.containsKey(plate.number);
                    final isAvailable = _plateHistory[plate.number];
                    final formattedNumber = _formatPlateNumber(plate.number);
                    final isSelected = _selectedPlates.contains(plate.number);

                    return Card(
                      elevation: isSelected ? 4 : 1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6), // 减小圆角
                      ),
                      color: isSelected
                          ? Theme.of(context).colorScheme.primaryContainer
                          : hasHistory
                              ? (isAvailable == true
                                  ? Colors.green.shade100
                                  : Colors.red.shade100)
                              : null,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(6), // 匹配卡片圆角
                        onTap: () => _togglePlateSelection(plate.number),
                        onLongPress: () {
                          Clipboard.setData(ClipboardData(text: plate.number));
                          NotificationManager.instance.showSuccess(
                            context,
                            localizations.plateModalCopiedToClipboard,
                          );
                        },
                        child: Stack(
                          children: [
                            // 使用Center确保FittedBox在卡片中完全居中
                            Center(
                              child: Padding(
                                padding: const EdgeInsets.all(2.0),
                                child: FittedBox(
                                  fit: BoxFit.scaleDown, // 缩放以适应空间
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        formattedNumber,
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12, // 恢复稍大的字体，FittedBox会自动缩放
                                          color: isSelected
                                              ? Theme.of(context).colorScheme.onPrimaryContainer
                                              : hasHistory
                                                  ? (isAvailable == true
                                                      ? Colors.green.shade900
                                                      : Colors.red.shade900)
                                                  : null,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        plate.type,
                                        style: TextStyle(
                                          fontSize: 9, // 恢复稍大的字体，FittedBox会自动缩放
                                          color: isSelected
                                              ? Theme.of(context).colorScheme.onPrimaryContainer
                                              : plate.type == 'Personalised'
                                                  ? Colors.red.shade900
                                                  : Colors.blue.shade700,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                top: 2, // 调整位置
                                right: 2,
                                child: Icon(
                                  Icons.check_circle,
                                  size: 14, // 减小图标
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                  ),
                ),
              ),

              // 分页控件 - 参考 history 页面设计
              if (_totalPages > 1) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4), // 减少垂直padding
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 上一页按钮
                      IconButton(
                        onPressed: _currentPage > 0 ? () => _goToPage(_currentPage - 1) : null,
                        icon: const Icon(Icons.chevron_left),
                        tooltip: localizations.plateModalPreviousPage,
                      ),

                      // 页码指示器
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: _buildPageIndicators(),
                          ),
                        ),
                      ),

                      // 下一页按钮
                      IconButton(
                        onPressed: _currentPage < _totalPages - 1
                            ? () => _goToPage(_currentPage + 1)
                            : null,
                        icon: const Icon(Icons.chevron_right),
                        tooltip: localizations.plateModalNextPage,
                      ),
                    ],
                  ),
                ),
              ],
            ],
            const SizedBox(height: 2),

            // 底部操作区域
            Container(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 12), // 调整padding使高度更合适
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, -2),
                    blurRadius: 8,
                    color: Colors.black.withValues(alpha: 0.1),
                  ),
                ],
              ),
              child: SafeArea(
                top: false, // 只保护底部
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedPlates.isEmpty
                            ? null
                            : () {
                                // 根据选择的车牌号码创建PlateNumber对象
                                final selectedPlateObjects = _allPlates
                                    .where((plate) => _selectedPlates.contains(plate.number))
                                    .toList();

                                Logger.instance.info('用户点击开始查询，返回 ${selectedPlateObjects.length} 个选中的车牌', 'PlateModal');
                                Navigator.pop(context, selectedPlateObjects);
                              },
                        icon: const Icon(Icons.search),
                        label: Text(_selectedPlates.isEmpty
                            ? localizations.plateModalStartQuery
                            : '${localizations.plateModalStartQuery} ${_selectedPlates.length}'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.lightBlue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          elevation: 4,
                          shadowColor: Colors.lightBlue.withValues(alpha: 0.4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _filteredPlates.isEmpty
                            ? null
                            : () {
                                // 根据当前排序规则选择前100个车牌
                                final first100Plates = _filteredPlates.take(PlateModalConfig.maxSelectionCount).toList();
                                Logger.instance.info('用户点击查询前${PlateModalConfig.maxSelectionCount}个，返回 ${first100Plates.length} 个车牌', 'PlateModal');
                                Navigator.pop(context, first100Plates);
                              },
                        icon: const Icon(Icons.search_off),
                        label: Text(localizations.plateModalQueryAll),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          elevation: 3,
                          shadowColor: Colors.deepOrange.withValues(alpha: 0.4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建页码指示器 - 智能分页，充分利用可用宽度
  List<Widget> _buildPageIndicators() {
    List<Widget> indicators = [];

    // 动态计算可以显示的页码数量，确保充分利用可用宽度
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - 32 - 96; // 减去padding和左右箭头按钮宽度
    final buttonWidth = 34; // 按钮宽度 (32 + 2 padding)

    // 计算最多可以显示多少个页码按钮
    int maxButtons = (availableWidth / buttonWidth).floor();
    maxButtons = maxButtons.clamp(3, 7); // 最少3个，最多7个

    // 如果总页数很少，直接显示所有页码
    if (_totalPages <= maxButtons) {
      for (int i = 0; i < _totalPages; i++) {
        indicators.add(_buildPageButton(i));
      }
      return indicators;
    }

    // 复杂情况：需要省略号的智能分页
    int centerButtons = maxButtons - 2; // 减去首页和末页
    int halfCenter = centerButtons ~/ 2;

    // 计算中心区域的起始和结束页码
    int centerStart = (_currentPage - halfCenter).clamp(1, _totalPages - centerButtons - 1);
    int centerEnd = (centerStart + centerButtons - 1).clamp(0, _totalPages - 2);

    // 调整centerStart以确保显示足够的按钮
    if (centerEnd - centerStart + 1 < centerButtons) {
      centerStart = (centerEnd - centerButtons + 1).clamp(1, _totalPages - 2);
    }

    // 添加第一页
    indicators.add(_buildPageButton(0));

    // 添加左侧省略号（如果需要）
    if (centerStart > 1) {
      indicators.add(_buildEllipsis());
    }

    // 添加中心页码
    for (int i = centerStart; i <= centerEnd; i++) {
      indicators.add(_buildPageButton(i));
    }

    // 添加右侧省略号（如果需要）
    if (centerEnd < _totalPages - 2) {
      indicators.add(_buildEllipsis());
    }

    // 添加最后一页
    if (_totalPages > 1) {
      indicators.add(_buildPageButton(_totalPages - 1));
    }

    return indicators;
  }

  Widget _buildEllipsis() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 2),
      child: SizedBox(
        width: 16,
        child: Text(
          '...',
          style: TextStyle(color: Colors.grey, fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // 构建页码按钮
  Widget _buildPageButton(int page) {
    final isCurrentPage = page == _currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2), // 恢复正常间距
      child: TextButton(
        onPressed: () => _goToPage(page),
        style: TextButton.styleFrom(
          backgroundColor: isCurrentPage
              ? Theme.of(context).colorScheme.primary
              : null,
          foregroundColor: isCurrentPage
              ? Theme.of(context).colorScheme.onPrimary
              : null,
          minimumSize: const Size(32, 32), // 减小按钮尺寸
          padding: const EdgeInsets.symmetric(horizontal: 6),
        ),
        child: Text('${page + 1}', style: const TextStyle(fontSize: 12)), // 减小字体
      ),
    );
  }
}
