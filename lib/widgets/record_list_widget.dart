import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/search_record.dart';
import '../services/database_service.dart';
import '../widgets/history_card.dart';
import '../config/history_config.dart';
import '../l10n/app_localizations.dart';
import '../utils/notification_manager.dart';

class RecordListWidget extends StatefulWidget {
  final bool showFilters; // 是否显示过滤器和排序选项
  final bool showFavoritesOnly; // 是否只显示收藏的记录
  final Function(SearchRecord)? onFavoriteToggle; // 收藏状态切换回调

  const RecordListWidget({
    super.key,
    this.showFilters = true,
    this.showFavoritesOnly = false,
    this.onFavoriteToggle,
  });

  @override
  State<RecordListWidget> createState() => _RecordListWidgetState();
}

class _RecordListWidgetState extends State<RecordListWidget> {
  final ScrollController _scrollController = ScrollController();
  int _pageSize = 20; // Will be updated based on screen height
  List<SearchRecord> _records = [];
  List<SearchRecord> _filteredRecords = [];
  List<SearchRecord> _currentPageRecords = [];
  bool _isLoading = false;

  // Pagination
  int _currentPage = 0;
  int get _totalPages => (_filteredRecords.length / _pageSize).ceil();

  // Filtering and sorting (only used when showFilters is true)
  HistoryFilterOption _currentFilter = HistoryFilterOption.all;
  HistorySortOption _currentSort = HistorySortOption.dateDesc;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Update page size based on screen dimensions
      final size = MediaQuery.of(context).size;
      _pageSize = HistoryConfig.getItemsPerPage(size.width, size.height);
      _loadAllData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadAllData() async {
    if (_isLoading) return;
    setState(() => _isLoading = true);

    try {
      final dbService = DatabaseService();
      List<SearchRecord> records;

      if (widget.showFavoritesOnly) {
        // Load favorite records only
        records = await dbService.getFavoriteRecords(
          limit: 10000, // Large limit to get all records
          offset: 0,
          sort: _currentSort,
        );
      } else {
        // Load all records with filtering
        records = await dbService.getRecords(
          limit: 10000, // Large limit to get all records
          offset: 0,
          filter: _currentFilter,
          sort: _currentSort,
        );
      }

      setState(() {
        _records = records;
        _applyFilterAndSort();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        NotificationManager.instance.showError(
          context,
          '加载记录失败: $e',
        );
      }
    }
  }

  void _applyFilterAndSort({bool resetPage = true}) {
    // Apply filtering and sorting (records are already filtered from database)
    _filteredRecords = List.from(_records);

    // Update pagination - only reset page when explicitly requested
    if (resetPage) {
      _currentPage = 0;
    }
    _updateCurrentPageRecords();
  }

  void _updateCurrentPageRecords() {
    final startIndex = _currentPage * _pageSize;
    final endIndex = (startIndex + _pageSize).clamp(0, _filteredRecords.length);

    _currentPageRecords = _filteredRecords.sublist(startIndex, endIndex);
  }

  // 计算统计数据
  Map<String, int> _calculateStats() {
    final stats = <String, int>{};

    // 总数
    stats['total'] = _filteredRecords.length;

    // 可用/不可用统计
    final available = _filteredRecords.where((r) => r.isAvailable).length;
    final unavailable = _filteredRecords.length - available;
    stats['available'] = available;
    stats['unavailable'] = unavailable;

    // 类型统计
    final custom = _filteredRecords.where((r) => r.plateType == 'Custom').length;
    final personalised = _filteredRecords.length - custom;
    stats['custom'] = custom;
    stats['personalised'] = personalised;

    // 可用类型统计
    final availableCustom = _filteredRecords.where((r) => r.isAvailable && r.plateType == 'Custom').length;
    final availablePersonalised = _filteredRecords.where((r) => r.isAvailable && r.plateType == 'Personalised').length;
    stats['availableCustom'] = availableCustom;
    stats['availablePersonalised'] = availablePersonalised;

    // 不可用类型统计
    final unavailableCustom = _filteredRecords.where((r) => !r.isAvailable && r.plateType == 'Custom').length;
    final unavailablePersonalised = _filteredRecords.where((r) => !r.isAvailable && r.plateType == 'Personalised').length;
    stats['unavailableCustom'] = unavailableCustom;
    stats['unavailablePersonalised'] = unavailablePersonalised;

    return stats;
  }

  // Filter and sort methods (only used when showFilters is true)
  void _onFilterChanged(HistoryFilterOption? filter) {
    if (filter != null && filter != _currentFilter) {
      setState(() {
        _currentFilter = filter;
      });
      _loadAllData();
    }
  }

  void _onSortChanged(HistorySortOption? sort) {
    if (sort != null && sort != _currentSort) {
      setState(() {
        _currentSort = sort;
      });
      _loadAllData();
    }
  }

  // Pagination methods
  void _goToPage(int page) {
    if (page >= 0 && page < _totalPages) {
      setState(() {
        _currentPage = page;
        _updateCurrentPageRecords();
      });
    }
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages - 1) {
      _goToPage(_currentPage + 1);
    }
  }

  void _goToPreviousPage() {
    if (_currentPage > 0) {
      _goToPage(_currentPage - 1);
    }
  }

  Future<void> _refreshRecord(SearchRecord record) async {
    try {
      final dbService = DatabaseService();
      final updatedRecord = await dbService.refreshRecord(record);

      setState(() {
        final index = _records.indexWhere((r) => r.id == updatedRecord.id);
        if (index != -1) {
          _records[index] = updatedRecord;
          // Don't reset page for record refresh - just update in place
          _updateFilteredRecordsInPlace(updatedRecord);
          _updateCurrentPageRecords();
        }
      });

      if (mounted) {
        final localizations = AppLocalizations.of(context);
        NotificationManager.instance.showSuccess(
          context,
          localizations.queryCompleteMessage,
        );
      }
    } catch (e) {
      if (mounted) {
        final localizations = AppLocalizations.of(context);
        NotificationManager.instance.showError(
          context,
          '${localizations.queryFailedMessage}: $e',
        );
      }
    }
  }

  /// 就地更新过滤记录中的特定记录，避免重新排序和分页重置
  void _updateFilteredRecordsInPlace(SearchRecord updatedRecord) {
    final filteredIndex = _filteredRecords.indexWhere((r) => r.id == updatedRecord.id);
    if (filteredIndex != -1) {
      _filteredRecords[filteredIndex] = updatedRecord;
    }
  }

  Future<void> _toggleFavorite(SearchRecord record) async {
    try {
      final dbService = DatabaseService();
      final updatedRecord = await dbService.toggleFavorite(record);

      setState(() {
        final index = _records.indexWhere((r) => r.id == updatedRecord.id);
        if (index != -1) {
          _records[index] = updatedRecord;

          // If showing favorites only and record is no longer favorite, remove it
          if (widget.showFavoritesOnly && !updatedRecord.isFavorite) {
            _records.removeAt(index);
            // Only reset page when removing items from favorites view
            _applyFilterAndSort();
          } else {
            // For normal favorite toggle, don't reset page - just update the filtered records
            _updateFilteredRecordsInPlace(updatedRecord);
            _updateCurrentPageRecords();
          }
        }
      });

      // Call the callback if provided
      if (widget.onFavoriteToggle != null) {
        widget.onFavoriteToggle!(updatedRecord);
      }

      if (mounted) {
        final localizations = AppLocalizations.of(context);
        final message = updatedRecord.isFavorite
            ? localizations.favoriteAddedMessage
            : localizations.favoriteRemovedMessage;
        NotificationManager.instance.showSuccess(
          context,
          message,
        );
      }
    } catch (e) {
      if (mounted) {
        NotificationManager.instance.showError(
          context,
          '操作失败: $e',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Filter and sort controls - only show when enabled and not loading
          if (widget.showFilters && !_isLoading) ...[
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 4),
                    blurRadius: 8,
                    color: Colors.black.withValues(alpha: 0.1),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<HistoryFilterOption>(
                          value: _currentFilter,
                          isExpanded: true,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).historyFilter,
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                          ),
                          items: HistoryFilterOption.values.map((filter) {
                            return DropdownMenuItem(
                              value: filter,
                              child: Text(
                                filter.getLabel(context),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            );
                          }).toList(),
                          onChanged: _onFilterChanged,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<HistorySortOption>(
                          value: _currentSort,
                          isExpanded: true,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context).historySort,
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                          ),
                          items: HistorySortOption.values.map((sort) {
                            return DropdownMenuItem(
                              value: sort,
                              child: Text(
                                sort.getLabel(context),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            );
                          }).toList(),
                          onChanged: _onSortChanged,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Statistics bar - only show when filters are enabled
                  _buildStatisticsBar(),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Content area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 4),
                    blurRadius: 8,
                    color: Colors.black.withValues(alpha: 0.1),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: _buildContent(),
              ),
            ),
          ),

          // Pagination controls
          if (!_isLoading && _filteredRecords.isNotEmpty && _totalPages > 1) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 4),
                    blurRadius: 8,
                    color: Colors.black.withValues(alpha: 0.1),
                  ),
                ],
              ),
              child: _buildPaginationControlsContent(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatisticsBar() {
    if (!widget.showFilters) return const SizedBox.shrink();

    final stats = _calculateStats();
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.lightBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.lightBlue.withValues(alpha: 0.3)),
      ),
      child: _buildCompactStats(stats, theme, AppLocalizations.of(context)),
    );
  }

  Widget _buildCompactStats(Map<String, int> stats, ThemeData theme, AppLocalizations localizations) {
    switch (_currentFilter) {
      case HistoryFilterOption.all:
        // 格式: 可用 100C/50P/150    不可用 200C/100P/300    总计: 450
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCompactStatGroup(
                localizations.available,
                stats['availableCustom']!,
                stats['availablePersonalised']!,
                stats['available']!,
                Colors.green,
                theme
              ),
              const SizedBox(width: 16),
              _buildCompactStatGroup(
                localizations.unavailable,
                stats['unavailableCustom']!,
                stats['unavailablePersonalised']!,
                stats['unavailable']!,
                Colors.red,
                theme
              ),
              const SizedBox(width: 16),
              _buildTotalStat(stats['total']!, theme),
            ],
          ),
        );

      case HistoryFilterOption.custom:
      case HistoryFilterOption.personalised:
        // 格式: 可用 150    不可用 300    总计: 450
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSimpleStatItem(localizations.available, stats['available']!, Colors.green, theme),
            _buildSimpleStatItem(localizations.unavailable, stats['unavailable']!, Colors.red, theme),
            _buildTotalStat(stats['total']!, theme),
          ],
        );

      case HistoryFilterOption.available:
      case HistoryFilterOption.unavailable:
        // 格式: C 200    P 250    总计: 450
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSimpleStatItem('C', stats['custom']!, Colors.grey.shade600, theme),
            _buildSimpleStatItem('P', stats['personalised']!, Colors.blue.shade700, theme),
            _buildTotalStat(stats['total']!, theme),
          ],
        );
    }
  }

  Widget _buildCompactStatGroup(String label, int customCount, int personalisedCount, int total, Color color, ThemeData theme) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$label ',
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: '${customCount}C',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          TextSpan(
            text: '/',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          TextSpan(
            text: '${personalisedCount}P',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
          TextSpan(
            text: '/',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          TextSpan(
            text: total.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleStatItem(String label, int count, Color color, ThemeData theme) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$label ',
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: count.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalStat(int total, ThemeData theme) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '${AppLocalizations.of(context).historyTotal}: ',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: total.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final localizations = AppLocalizations.of(context);

    if (_records.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.showFavoritesOnly ? Icons.favorite_border : Icons.history,
              size: 64,
              color: Colors.grey
            ),
            const SizedBox(height: 16),
            Text(
              widget.showFavoritesOnly ? localizations.favoritesEmpty : localizations.historyEmpty,
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (_filteredRecords.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.filter_list_off, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              localizations.historyEmptyFiltered,
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAllData,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        child: GridView.builder(
          controller: _scrollController,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: HistoryConfig.getCardsPerRow(MediaQuery.of(context).size.width),
            childAspectRatio: HistoryConfig.getActualAspectRatio(MediaQuery.of(context).size.width),
            crossAxisSpacing: HistoryConfig.cardSpacing,
            mainAxisSpacing: HistoryConfig.cardSpacing,
          ),
          itemCount: _currentPageRecords.length,
          itemBuilder: (context, index) {
            final record = _currentPageRecords[index];

            return HistoryCard(
              record: record,
              onRefresh: () => _refreshRecord(record),
              onFavoriteToggle: () => _toggleFavorite(record),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPaginationControlsContent() {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context);

    // 如果没有页面，不显示分页控件
    if (_totalPages <= 0) {
      return const SizedBox.shrink();
    }

    // Calculate visible page numbers (show fewer to minimize horizontal scrolling)
    const maxVisiblePages = 5; // Reduced from 7 to 5
    int startPage = (_currentPage - maxVisiblePages ~/ 2).clamp(0, math.max(0, _totalPages - maxVisiblePages));
    int endPage = (startPage + maxVisiblePages - 1).clamp(0, _totalPages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage < maxVisiblePages - 1 && _totalPages >= maxVisiblePages) {
      startPage = (endPage - maxVisiblePages + 1).clamp(0, math.max(0, _totalPages - maxVisiblePages));
    }

    return Row(
      children: [
        // Previous button
        IconButton(
            onPressed: _currentPage > 0 ? _goToPreviousPage : null,
            icon: const Icon(Icons.chevron_left),
            tooltip: localizations.plateModalPreviousPage,
          ),

          // Page numbers - evenly distributed
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Changed to spaceEvenly for even distribution
              children: [
                for (int i = startPage; i <= endPage; i++)
                  Flexible(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      child: TextButton(
                        onPressed: () => _goToPage(i),
                        style: TextButton.styleFrom(
                          backgroundColor: i == _currentPage
                              ? Colors.lightBlue
                              : Colors.transparent,
                          foregroundColor: i == _currentPage
                              ? Colors.white
                              : theme.colorScheme.onSurface,
                          minimumSize: const Size(32, 32),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                        child: Text(
                          '${i + 1}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Next button
          IconButton(
            onPressed: _currentPage < _totalPages - 1 ? _goToNextPage : null,
            icon: const Icon(Icons.chevron_right),
            tooltip: localizations.plateModalNextPage,
          ),
        ],
      );
    }
}