import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/search_record.dart';
import '../config/history_config.dart';
import '../l10n/app_localizations.dart';
import '../enums/vehicle_type.dart';

class HistoryContextMenu extends StatelessWidget {
  final SearchRecord record;
  final VoidCallback onRefresh;
  final VoidCallback? onFavoriteToggle; // 收藏状态切换回调

  const HistoryContextMenu({
    super.key,
    required this.record,
    required this.onRefresh,
    this.onFavoriteToggle,
  });

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context);

    return Dialog(
      child: Container(
        width: HistoryConfig.contextMenuWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 8),
              blurRadius: 24,
              color: Colors.black.withValues(alpha: 0.15),
            ),
          ],
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.lightBlue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    localizations.plateDetailsTitle,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.lightBlue,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  iconSize: 20,
                  color: Colors.grey[600],
                ),
              ],
            ),
            const SizedBox(height: 16),
            const SizedBox(height: 4),
          
            // 基本信息组
            _buildInfoRow(
              context,
              localizations.plateNumberLabel,
              record.plateNumber,
              Icons.confirmation_number,
            ),

            _buildInfoRow(
              context,
              localizations.plateTypeLabel,
              record.plateType,
              Icons.category,
            ),

            // Vehicle type (only show if available)
            if (record.vehicleType != null)
              _buildInfoRow(
                context,
                localizations.vehicleTypeContextLabel,
                VehicleType.getLocalizedDisplayName(context, record.vehicleType),
                Icons.directions_car,
              ),

            // 状态信息组 - 简单分隔
            const Divider(height: 24, color: Colors.grey),

            _buildInfoRow(
              context,
              localizations.availabilityLabel,
              record.isAvailable
                ? RegExp(r'^[A-Z]{1}\d{5}$').hasMatch(record.plateNumber)
                  ? localizations.styleUnavailableMessage
                  : localizations.availableStatus
                : localizations.unavailableStatus,
              record.isAvailable ? Icons.check_circle : Icons.cancel,
              valueColor: record.isAvailable
                ? RegExp(r'^[A-Z]{1}\d{5}$').hasMatch(record.plateNumber)
                  ? Colors.blue
                  : Colors.green
                : Colors.red,
            ),

            _buildInfoRow(
              context,
              localizations.lastQueryTimeLabel,
              _formatDateTime(record.searchTime),
              Icons.access_time,
            ),

            _buildInfoRow(
              context,
              localizations.favoriteStatusLabel,
              record.isFavorite ? localizations.favoriteStatusFavorited : localizations.favoriteStatusNotFavorited,
              record.isFavorite ? Icons.favorite : Icons.favorite_border,
              valueColor: record.isFavorite ? Colors.red : Colors.grey,
            ),

            const SizedBox(height: 16),
          
            // Action buttons
            Column(
              children: [
                // First row: Favorite toggle button
                if (onFavoriteToggle != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onFavoriteToggle!();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: record.isFavorite ? Colors.grey[100] : Colors.red[50],
                        foregroundColor: record.isFavorite ? Colors.grey[700] : Colors.red,
                        elevation: 1,
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: record.isFavorite ? Colors.grey[300]! : Colors.red[200]!,
                            width: 1,
                          ),
                        ),
                      ),
                      icon: Icon(
                        record.isFavorite ? Icons.favorite_border : Icons.favorite,
                        size: 16,
                      ),
                      label: Text(
                        record.isFavorite ? localizations.favoriteRemove : localizations.favoriteAdd,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ),

                if (onFavoriteToggle != null) const SizedBox(height: 10),

                // Second row: Close and Refresh buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                          foregroundColor: Colors.grey[700],
                          elevation: 1,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.grey[300]!, width: 1),
                          ),
                        ),
                        icon: const Icon(Icons.close, size: 16),
                        label: Text(
                          localizations.closeButton,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          onRefresh();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.lightBlue,
                          foregroundColor: Colors.white,
                          elevation: 2,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shadowColor: Colors.lightBlue.withValues(alpha: 0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.refresh, size: 16),
                        label: Text(
                          localizations.retryButton,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.lightBlue,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: valueColor ?? Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
