import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/webview_service.dart';
import '../utils/logger.dart';

/// Cloudflare验证对话框
/// 用于显示WebView进行手动验证
class CloudflareVerificationDialog extends StatefulWidget {
  final String url;
  final String title;
  final Function(bool success)? onVerificationComplete;

  const CloudflareVerificationDialog({
    Key? key,
    required this.url,
    this.title = 'Cloudflare验证',
    this.onVerificationComplete,
  }) : super(key: key);

  @override
  State<CloudflareVerificationDialog> createState() => _CloudflareVerificationDialogState();
}

class _CloudflareVerificationDialogState extends State<CloudflareVerificationDialog> {
  late WebViewController _controller;
  bool _isLoading = true;
  String _statusMessage = '正在加载验证页面...';
  bool _verificationCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewService.instance.createController(
      initialUrl: widget.url,
      onPageStarted: (url) {
        setState(() {
          _isLoading = true;
          _statusMessage = '正在加载页面...';
        });
      },
      onPageFinished: (url) async {
        setState(() {
          _isLoading = false;
        });

        // 检查是否仍在Cloudflare挑战页面
        final hasChallenge = await WebViewService.instance.hasCloudflareChallenge();
        
        if (hasChallenge) {
          setState(() {
            _statusMessage = '请完成Cloudflare验证';
          });
          
          // 开始等待验证完成
          _waitForVerification();
        } else {
          // 验证已完成
          _onVerificationSuccess();
        }
      },
      onWebResourceError: (error) {
        setState(() {
          _isLoading = false;
          _statusMessage = '加载失败: ${error.description}';
        });
      },
    );
  }

  void _waitForVerification() async {
    try {
      final success = await WebViewService.instance.waitForCloudflareVerification(
        timeout: const Duration(minutes: 5),
        onStatusUpdate: (status) {
          if (mounted) {
            setState(() {
              _statusMessage = status;
            });
          }
        },
      );

      if (success) {
        _onVerificationSuccess();
      } else {
        _onVerificationFailed();
      }
    } catch (e) {
      Logger.instance.error('等待Cloudflare验证时发生错误: $e', 'CloudflareVerificationDialog');
      _onVerificationFailed();
    }
  }

  void _onVerificationSuccess() {
    if (_verificationCompleted) return;
    
    setState(() {
      _verificationCompleted = true;
      _statusMessage = '验证成功！';
    });

    Logger.instance.info('Cloudflare验证成功', 'CloudflareVerificationDialog');
    
    // 延迟关闭对话框
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop();
        widget.onVerificationComplete?.call(true);
      }
    });
  }

  void _onVerificationFailed() {
    if (_verificationCompleted) return;
    
    setState(() {
      _verificationCompleted = true;
      _statusMessage = '验证失败或超时';
    });

    Logger.instance.warning('Cloudflare验证失败', 'CloudflareVerificationDialog');
  }

  void _refresh() {
    _controller.reload();
    setState(() {
      _isLoading = true;
      _statusMessage = '正在刷新页面...';
      _verificationCompleted = false;
    });
  }

  void _goBack() {
    _controller.goBack();
  }

  void _goForward() {
    _controller.goForward();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onVerificationComplete?.call(false);
                    },
                  ),
                ],
              ),
            ),
            
            // 状态栏
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: _verificationCompleted 
                ? (_statusMessage.contains('成功') ? Colors.green[100] : Colors.red[100])
                : Colors.blue[100],
              child: Row(
                children: [
                  if (_isLoading)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  if (_isLoading) const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: TextStyle(
                        color: _verificationCompleted 
                          ? (_statusMessage.contains('成功') ? Colors.green[800] : Colors.red[800])
                          : Colors.blue[800],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 工具栏
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _goBack,
                    tooltip: '后退',
                  ),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: _goForward,
                    tooltip: '前进',
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _refresh,
                    tooltip: '刷新',
                  ),
                  const Spacer(),
                  if (_verificationCompleted && !_statusMessage.contains('成功'))
                    ElevatedButton(
                      onPressed: _refresh,
                      child: const Text('重试'),
                    ),
                ],
              ),
            ),
            
            // WebView
            Expanded(
              child: WebViewWidget(controller: _controller),
            ),
            
            // 底部提示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: Colors.grey[100],
              child: Text(
                '请完成页面中的验证步骤。验证完成后，对话框将自动关闭。',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 显示Cloudflare验证对话框的便捷方法
Future<bool> showCloudflareVerificationDialog(
  BuildContext context, {
  required String url,
  String title = 'Cloudflare验证',
}) async {
  final completer = Completer<bool>();
  
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => CloudflareVerificationDialog(
      url: url,
      title: title,
      onVerificationComplete: (success) {
        completer.complete(success);
      },
    ),
  );
  
  return completer.future;
}
