import 'package:flutter/material.dart';
import '../models/search_record.dart';
import 'history_context_menu.dart';

class HistoryCard extends StatelessWidget {
  final SearchRecord record;
  final VoidCallback onRefresh;
  final VoidCallback? onFavoriteToggle; // 收藏状态切换回调

  const HistoryCard({
    super.key,
    required this.record,
    required this.onRefresh,
    this.onFavoriteToggle,
  });

  // Format plate number for display
  String _formatPlateNumber(String plate) {
    final patterns = [
      {
        'pattern': RegExp(r'^[A-Z]{2}\d{2}[A-Z]{2}$'),
        'format': (String p) => '${p.substring(0, 2)}.${p.substring(2, 4)}.${p.substring(4)}',
      },
      {
        'pattern': RegExp(r'^[A-Z]{2}\d{3,4}$'),
        'format': (String p) => '${p.substring(0, 2)}.${p.substring(2)}',
      },
      {
        'pattern': RegExp(r'^[A-Z]{3}\d{3}$'),
        'format': (String p) => '${p.substring(0, 3)}.${p.substring(3)}',
      },
      {
        'pattern': RegExp(r'^[A-Z]{3}\d{2}[A-Z]$'),
        'format': (String p) => '${p.substring(0, 3)}.${p.substring(3, 5)}.${p.substring(5)}',
      },
      {
        'pattern': RegExp(r'^\d{2}[A-Z]{3,4}$'),
        'format': (String p) => '${p.substring(0, 2)}.${p.substring(2)}',
      },
      {
        'pattern': RegExp(r'^\d{3}[A-Z]{3}$'),
        'format': (String p) => '${p.substring(0, 3)}.${p.substring(3)}',
      },
    ];

    for (var pattern in patterns) {
      final regex = pattern['pattern'] as RegExp?;
      final formatter = pattern['format'] as String Function(String)?;
      if (regex != null && formatter != null && regex.hasMatch(plate)) {
        return formatter(plate);
      }
    }
    return plate;
  }

  void _showContextMenu(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => HistoryContextMenu(
        record: record,
        onRefresh: onRefresh,
        onFavoriteToggle: onFavoriteToggle,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final formattedNumber = _formatPlateNumber(record.plateNumber);

    // Determine card colors based on availability
    Color cardColor;
    Color textColor;

    if (record.isAvailable) {
      if (RegExp(r'^[A-Z]{1}\d{5}$').hasMatch(record.plateNumber)) {
        // 如果是1位字母加5位数字的格式，使用蓝色
        cardColor = Colors.blue.shade100;
        textColor = Colors.blue.shade900;
      } else {
        // 其他可用车牌使用绿色
        cardColor = Colors.green.shade100;
        textColor = Colors.green.shade900;
      }
    } else {
      cardColor = Colors.red.shade100;
      textColor = Colors.red.shade900;
    }

    return Card(
      elevation: 1,
      color: cardColor,
      margin: const EdgeInsets.all(2.0), // 减少卡片外边距
      child: InkWell(
        onLongPress: () => _showContextMenu(context),
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 4.0), // 大幅减少内边距
          child: Stack(
            children: [
              // Main content - centered
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Plate number
                    Text(
                      formattedNumber,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12, // 稍微减小字体
                        color: textColor,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 1), // 减少间距

                    // Plate type
                    Text(
                      record.plateType,
                      style: TextStyle(
                        fontSize: 9, // 减小字体
                        color: record.plateType == 'Personalised'
                            ? Colors.red.shade900  // 匹配plate modal的红色
                            : Colors.blue.shade700, // 匹配plate modal的蓝色
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Favorite button
              if (onFavoriteToggle != null)
                Positioned(
                  top: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: onFavoriteToggle,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      child: Icon(
                        record.isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 15,
                        color: record.isFavorite ? Colors.red : Colors.grey.shade600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
