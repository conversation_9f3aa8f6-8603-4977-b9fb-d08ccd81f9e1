import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class SearchProgressBar extends StatelessWidget {
  final double progress;
  final int completed;
  final int total;
  final int available;
  final bool isSearching;
  final VoidCallback? onCancel;

  const SearchProgressBar({
    super.key,
    required this.progress,
    required this.completed,
    required this.total,
    required this.available,
    this.isSearching = false,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  available > 0 ? Colors.green : Colors.blue,
                ),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            // 只在查询进行中时显示终止按钮
            if (isSearching && onCancel != null) ...[
              const SizedBox(width: 12),
              IconButton(
                onPressed: onCancel,
                icon: const Icon(Icons.stop),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(8),
                ),
                tooltip: localizations.terminateButton,
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Text('${localizations.progressLabel}: $completed/$total'),
            const SizedBox(width: 16),
            Text('${localizations.availableLabel}: $available'),
          ],
        ),
      ],
    );
  }
}
