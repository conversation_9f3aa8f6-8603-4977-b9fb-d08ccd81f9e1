import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';
import 'cookie_manager.dart';

/// WebView服务
/// 用于处理需要手动验证的情况，如CAPTCHA解决和浏览器验证
class WebViewService {
  static WebViewService? _instance;
  static WebViewService get instance => _instance ??= WebViewService._();
  
  WebViewController? _controller;
  bool _isInitialized = false;
  
  WebViewService._();
  
  /// 初始化WebView服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // WebView初始化（移除不支持的clearCookies调用）
      _isInitialized = true;
      Logger.instance.info('WebView服务初始化成功', 'WebViewService');
    } catch (e) {
      Logger.instance.error('WebView服务初始化失败: $e', 'WebViewService');
    }
  }
  
  /// 创建WebView控制器
  WebViewController createController({
    required String initialUrl,
    Function(String)? onPageFinished,
    Function(String)? onPageStarted,
    Function(WebResourceError)? onWebResourceError,
  }) {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            Logger.instance.debug('WebView开始加载: $url', 'WebViewService');
            onPageStarted?.call(url);
          },
          onPageFinished: (String url) async {
            Logger.instance.debug('WebView加载完成: $url', 'WebViewService');
            
            // 提取并保存Cookie
            await _extractAndSaveCookies(url);
            
            onPageFinished?.call(url);
          },
          onWebResourceError: (WebResourceError error) {
            Logger.instance.error('WebView资源错误: ${error.description}', 'WebViewService');
            onWebResourceError?.call(error);
          },
          onNavigationRequest: (NavigationRequest request) {
            Logger.instance.debug('WebView导航请求: ${request.url}', 'WebViewService');
            return NavigationDecision.navigate;
          },
        ),
      );
    
    // 设置用户代理
    _setUserAgent();
    
    // 加载初始URL
    _controller!.loadRequest(Uri.parse(initialUrl));
    
    return _controller!;
  }
  
  /// 设置用户代理
  Future<void> _setUserAgent() async {
    if (_controller == null) return;
    
    try {
      // 使用与HTTP请求相同的用户代理
      String userAgent;
      
      if (Platform.isAndroid) {
        userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
      } else if (Platform.isIOS) {
        userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
      } else {
        userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
      }
      
      await _controller!.setUserAgent(userAgent);
      Logger.instance.debug('WebView用户代理设置为: $userAgent', 'WebViewService');
    } catch (e) {
      Logger.instance.error('设置WebView用户代理失败: $e', 'WebViewService');
    }
  }
  
  /// 提取并保存Cookie
  Future<void> _extractAndSaveCookies(String url) async {
    try {
      if (_controller == null) return;
      
      // 执行JavaScript获取Cookie
      final cookieString = await _controller!.runJavaScriptReturningResult(
        'document.cookie'
      ) as String;
      
      if (cookieString.isNotEmpty && cookieString != 'null') {
        // 清理Cookie字符串（移除引号）
        final cleanCookieString = cookieString.replaceAll('"', '');
        
        // 解析Cookie并保存到Cookie管理器
        final cookies = CookieManager.instance.parseCookieString(cleanCookieString);
        
        if (cookies.isNotEmpty) {
          final uri = Uri.parse(url);
          await CookieManager.instance.saveCookies(uri, cookies);
          Logger.instance.info('从WebView提取并保存了 ${cookies.length} 个Cookie', 'WebViewService');
        }
      }
    } catch (e) {
      Logger.instance.error('提取WebView Cookie失败: $e', 'WebViewService');
    }
  }
  
  /// 注入Cookie到WebView
  Future<void> injectCookies(String url) async {
    try {
      if (_controller == null) return;
      
      final uri = Uri.parse(url);
      final cookies = await CookieManager.instance.getCookies(uri);
      
      if (cookies.isNotEmpty) {
        final cookieString = CookieManager.instance.cookiesToString(cookies);
        
        // 通过JavaScript设置Cookie
        await _controller!.runJavaScript(
          'document.cookie = "$cookieString";'
        );
        
        Logger.instance.info('向WebView注入了 ${cookies.length} 个Cookie', 'WebViewService');
      }
    } catch (e) {
      Logger.instance.error('向WebView注入Cookie失败: $e', 'WebViewService');
    }
  }
  
  /// 检查页面是否包含Cloudflare挑战
  Future<bool> hasCloudflareChallenge() async {
    try {
      if (_controller == null) return false;
      
      // 检查页面标题
      final title = await _controller!.getTitle();
      if (title != null && title.toLowerCase().contains('cloudflare')) {
        return true;
      }
      
      // 检查页面内容中的Cloudflare标识
      final hasChallenge = await _controller!.runJavaScriptReturningResult(
        '''
        (function() {
          var body = document.body.innerHTML.toLowerCase();
          return body.includes('cloudflare') || 
                 body.includes('checking your browser') ||
                 body.includes('please wait') ||
                 body.includes('ddos protection') ||
                 document.querySelector('.cf-browser-verification') !== null ||
                 document.querySelector('#cf-wrapper') !== null;
        })();
        '''
      );
      
      return hasChallenge == true;
    } catch (e) {
      Logger.instance.error('检查Cloudflare挑战失败: $e', 'WebViewService');
      return false;
    }
  }
  
  /// 等待Cloudflare验证完成
  Future<bool> waitForCloudflareVerification({
    Duration timeout = const Duration(minutes: 2),
    Function(String)? onStatusUpdate,
  }) async {
    try {
      if (_controller == null) return false;
      
      final startTime = DateTime.now();
      
      while (DateTime.now().difference(startTime) < timeout) {
        // 检查是否仍在Cloudflare挑战页面
        final hasChallenge = await hasCloudflareChallenge();
        
        if (!hasChallenge) {
          Logger.instance.info('Cloudflare验证已完成', 'WebViewService');
          
          // 提取验证后的Cookie
          final currentUrl = await _controller!.currentUrl();
          if (currentUrl != null) {
            await _extractAndSaveCookies(currentUrl);
          }
          
          return true;
        }
        
        // 更新状态
        onStatusUpdate?.call('正在等待Cloudflare验证完成...');
        
        // 等待一段时间后再次检查
        await Future.delayed(const Duration(seconds: 2));
      }
      
      Logger.instance.warning('Cloudflare验证超时', 'WebViewService');
      return false;
    } catch (e) {
      Logger.instance.error('等待Cloudflare验证失败: $e', 'WebViewService');
      return false;
    }
  }
  
  /// 获取当前页面的认证令牌
  Future<Map<String, String>?> extractAuthTokens() async {
    try {
      if (_controller == null) return null;
      
      // 尝试从页面中提取常见的认证令牌
      final tokens = await _controller!.runJavaScriptReturningResult(
        '''
        (function() {
          var tokens = {};
          
          // 查找CSRF令牌
          var csrfMeta = document.querySelector('meta[name="csrf-token"]');
          if (csrfMeta) {
            tokens.csrf = csrfMeta.getAttribute('content');
          }
          
          // 查找其他认证令牌
          var authMeta = document.querySelector('meta[name="auth-token"]');
          if (authMeta) {
            tokens.auth = authMeta.getAttribute('content');
          }
          
          // 查找隐藏的表单字段
          var hiddenInputs = document.querySelectorAll('input[type="hidden"]');
          hiddenInputs.forEach(function(input) {
            if (input.name && input.value) {
              tokens[input.name] = input.value;
            }
          });
          
          return JSON.stringify(tokens);
        })();
        '''
      ) as String;
      
      if (tokens.isNotEmpty && tokens != 'null') {
        // 解析JSON字符串
        final Map<String, dynamic> parsedTokens = {};
        // 这里需要手动解析，因为WebView返回的是字符串
        // 简化处理，实际应用中可能需要更复杂的JSON解析
        
        Logger.instance.debug('提取到认证令牌: $tokens', 'WebViewService');
        return {'raw_tokens': tokens};
      }
      
      return null;
    } catch (e) {
      Logger.instance.error('提取认证令牌失败: $e', 'WebViewService');
      return null;
    }
  }
  
  /// 清理WebView数据
  Future<void> clearData() async {
    try {
      if (_controller == null) return;
      
      await _controller!.clearCache();
      await _controller!.clearLocalStorage();
      
      Logger.instance.info('WebView数据已清理', 'WebViewService');
    } catch (e) {
      Logger.instance.error('清理WebView数据失败: $e', 'WebViewService');
    }
  }
  
  /// 获取当前控制器
  WebViewController? get controller => _controller;
  
  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
}
