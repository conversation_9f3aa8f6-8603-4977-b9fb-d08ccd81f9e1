import 'dart:io';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

/// Cookie管理服务
/// 提供持久化Cookie存储和管理功能，用于维护浏览器会话状态
class CookieManager {
  static CookieManager? _instance;
  static CookieManager get instance => _instance ??= CookieManager._();
  
  CookieJar? _cookieJar;
  bool _isInitialized = false;
  
  CookieManager._();
  
  /// 初始化Cookie管理器
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final cookiePath = '${directory.path}/cookies';
      
      // 创建持久化Cookie Jar
      _cookieJar = PersistCookieJar(
        storage: FileStorage(cookiePath),
        persistSession: true, // 持久化会话Cookie
        ignoreExpires: false, // 遵守Cookie过期时间
      );
      
      _isInitialized = true;
      Logger.instance.info('Cookie管理器初始化成功，存储路径: $cookiePath', 'CookieManager');
      
      // 记录现有Cookie数量
      await _logExistingCookies();
      
    } catch (e) {
      Logger.instance.error('Cookie管理器初始化失败: $e', 'CookieManager');
      // 如果持久化失败，使用内存Cookie Jar作为回退
      _cookieJar = CookieJar();
      _isInitialized = true;
      Logger.instance.warning('使用内存Cookie Jar作为回退', 'CookieManager');
    }
  }
  
  /// 获取Cookie Jar实例
  CookieJar? get cookieJar {
    if (!_isInitialized) {
      Logger.instance.warning('Cookie管理器未初始化，请先调用initialize()', 'CookieManager');
      return null;
    }
    return _cookieJar;
  }
  
  /// 获取指定URL的所有Cookie
  Future<List<Cookie>> getCookies(Uri uri) async {
    if (!_isInitialized || _cookieJar == null) {
      Logger.instance.warning('Cookie管理器未初始化', 'CookieManager');
      return [];
    }
    
    try {
      final cookies = await _cookieJar!.loadForRequest(uri);
      Logger.instance.debug('获取到 ${cookies.length} 个Cookie for ${uri.host}', 'CookieManager');
      return cookies;
    } catch (e) {
      Logger.instance.error('获取Cookie失败: $e', 'CookieManager');
      return [];
    }
  }
  
  /// 保存Cookie到指定URL
  Future<void> saveCookies(Uri uri, List<Cookie> cookies) async {
    if (!_isInitialized || _cookieJar == null) {
      Logger.instance.warning('Cookie管理器未初始化', 'CookieManager');
      return;
    }
    
    try {
      await _cookieJar!.saveFromResponse(uri, cookies);
      Logger.instance.debug('保存了 ${cookies.length} 个Cookie for ${uri.host}', 'CookieManager');
      
      // 在调试模式下记录Cookie详情
      if (kDebugMode) {
        for (final cookie in cookies) {
          Logger.instance.debug('保存Cookie: ${cookie.name}=${cookie.value}', 'CookieManager');
        }
      }
    } catch (e) {
      Logger.instance.error('保存Cookie失败: $e', 'CookieManager');
    }
  }
  
  /// 清除指定域名的所有Cookie
  Future<void> clearCookies(String domain) async {
    if (!_isInitialized || _cookieJar == null) {
      Logger.instance.warning('Cookie管理器未初始化', 'CookieManager');
      return;
    }
    
    try {
      await _cookieJar!.delete(Uri.parse('https://$domain'));
      Logger.instance.info('已清除域名 $domain 的所有Cookie', 'CookieManager');
    } catch (e) {
      Logger.instance.error('清除Cookie失败: $e', 'CookieManager');
    }
  }
  
  /// 清除所有Cookie
  Future<void> clearAllCookies() async {
    if (!_isInitialized || _cookieJar == null) {
      Logger.instance.warning('Cookie管理器未初始化', 'CookieManager');
      return;
    }
    
    try {
      await _cookieJar!.deleteAll();
      Logger.instance.info('已清除所有Cookie', 'CookieManager');
    } catch (e) {
      Logger.instance.error('清除所有Cookie失败: $e', 'CookieManager');
    }
  }
  
  /// 从Cookie字符串解析Cookie列表
  List<Cookie> parseCookieString(String cookieString) {
    final cookies = <Cookie>[];
    
    try {
      final cookiePairs = cookieString.split(';');
      
      for (final pair in cookiePairs) {
        final trimmedPair = pair.trim();
        if (trimmedPair.isEmpty) continue;
        
        final equalIndex = trimmedPair.indexOf('=');
        if (equalIndex == -1) continue;
        
        final name = trimmedPair.substring(0, equalIndex).trim();
        final value = trimmedPair.substring(equalIndex + 1).trim();
        
        if (name.isNotEmpty) {
          final cookie = Cookie(name, value);
          cookies.add(cookie);
        }
      }
      
      Logger.instance.debug('解析Cookie字符串得到 ${cookies.length} 个Cookie', 'CookieManager');
    } catch (e) {
      Logger.instance.error('解析Cookie字符串失败: $e', 'CookieManager');
    }
    
    return cookies;
  }
  
  /// 将Cookie列表转换为字符串
  String cookiesToString(List<Cookie> cookies) {
    try {
      final cookieStrings = cookies.map((cookie) => '${cookie.name}=${cookie.value}').toList();
      return cookieStrings.join('; ');
    } catch (e) {
      Logger.instance.error('Cookie转换为字符串失败: $e', 'CookieManager');
      return '';
    }
  }
  
  /// 记录现有Cookie信息（仅调试模式）
  Future<void> _logExistingCookies() async {
    if (!kDebugMode || !_isInitialized || _cookieJar == null) return;
    
    try {
      // 尝试获取主要域名的Cookie
      final domains = ['vplates.com.au', 'cloudflare.com'];
      
      for (final domain in domains) {
        final uri = Uri.parse('https://$domain');
        final cookies = await _cookieJar!.loadForRequest(uri);
        
        if (cookies.isNotEmpty) {
          Logger.instance.debug('域名 $domain 现有 ${cookies.length} 个Cookie', 'CookieManager');
          for (final cookie in cookies) {
            Logger.instance.debug('  ${cookie.name}=${cookie.value}', 'CookieManager');
          }
        }
      }
    } catch (e) {
      Logger.instance.debug('记录现有Cookie时出错: $e', 'CookieManager');
    }
  }
  
  /// 获取Cookie统计信息
  Future<Map<String, int>> getCookieStats() async {
    final stats = <String, int>{};
    
    if (!_isInitialized || _cookieJar == null) {
      return stats;
    }
    
    try {
      final domains = ['vplates.com.au', 'cloudflare.com'];
      
      for (final domain in domains) {
        final uri = Uri.parse('https://$domain');
        final cookies = await _cookieJar!.loadForRequest(uri);
        stats[domain] = cookies.length;
      }
    } catch (e) {
      Logger.instance.error('获取Cookie统计信息失败: $e', 'CookieManager');
    }
    
    return stats;
  }
}
