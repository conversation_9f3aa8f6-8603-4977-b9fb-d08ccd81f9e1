import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/logger.dart';

class ApiLogger {
  static const String _logFileName = 'api_requests.log';
  static File? _logFile;
  
  /// 初始化日志文件
  static Future<void> initialize() async {
    // 仅在Debug模式下初始化详细日志记录
    if (!kDebugMode) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      _logFile = File('${directory.path}/$_logFileName');

      // 确保日志文件存在
      if (!await _logFile!.exists()) {
        await _logFile!.create(recursive: true);
      }

      // 记录初始化信息
      await _writeLog('=== API Logger 初始化 ===');
      await _writeLog('时间: ${DateTime.now().toIso8601String()}');
      await _writeLog('平台: ${Platform.operatingSystem}');
      await _writeLog('是否为Release模式: $kReleaseMode');
      await _writeLog('是否为Debug模式: $kDebugMode');
      await _writeLog('是否为Profile模式: $kProfileMode');
      await _writeLog('日志文件路径: ${_logFile!.path}');
      await _writeLog('========================\n');
    } catch (e) {
      Logger.instance.error('初始化API日志记录器失败: $e', 'ApiLogger');
    }
  }
  
  /// 记录请求信息
  static Future<void> logRequest({
    required String method,
    required String url,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    dynamic data,
    String? plateNumber,
  }) async {
    // 仅在Debug模式下记录详细日志
    if (!kDebugMode) return;

    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = StringBuffer();

      logEntry.writeln('=== 请求开始 ===');
      logEntry.writeln('时间: $timestamp');
      logEntry.writeln('车牌号: ${plateNumber ?? "未指定"}');
      logEntry.writeln('方法: $method');
      logEntry.writeln('URL: $url');

      if (queryParameters != null && queryParameters.isNotEmpty) {
        logEntry.writeln('查询参数: ${_formatJson(queryParameters)}');
      }

      if (headers != null && headers.isNotEmpty) {
        logEntry.writeln('请求头:');
        headers.forEach((key, value) {
          logEntry.writeln('  $key: $value');
        });
      }

      if (data != null) {
        logEntry.writeln('请求体: ${_formatJson(data)}');
      }

      logEntry.writeln('================\n');

      await _writeLog(logEntry.toString());

      // 同时输出到控制台
      Logger.instance.debug(logEntry.toString(), 'ApiLogger');
    } catch (e) {
      Logger.instance.error('记录请求日志失败: $e', 'ApiLogger');
    }
  }
  
  /// 记录响应信息
  static Future<void> logResponse({
    required String url,
    required int? statusCode,
    Map<String, dynamic>? headers,
    dynamic data,
    String? plateNumber,
    Duration? duration,
  }) async {
    // 仅在Debug模式下记录详细日志
    if (!kDebugMode) return;

    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = StringBuffer();

      logEntry.writeln('=== 响应接收 ===');
      logEntry.writeln('时间: $timestamp');
      logEntry.writeln('车牌号: ${plateNumber ?? "未指定"}');
      logEntry.writeln('URL: $url');
      logEntry.writeln('状态码: $statusCode');

      if (duration != null) {
        logEntry.writeln('耗时: ${duration.inMilliseconds}ms');
      }

      if (headers != null && headers.isNotEmpty) {
        logEntry.writeln('响应头:');
        headers.forEach((key, value) {
          logEntry.writeln('  $key: $value');
        });
      }

      if (data != null) {
        logEntry.writeln('响应体: ${_formatJson(data)}');
      }

      logEntry.writeln('================\n');

      await _writeLog(logEntry.toString());

      // 同时输出到控制台
      Logger.instance.debug(logEntry.toString(), 'ApiLogger');
    } catch (e) {
      Logger.instance.error('记录响应日志失败: $e', 'ApiLogger');
    }
  }
  
  /// 记录错误信息
  static Future<void> logError({
    required String url,
    required dynamic error,
    String? plateNumber,
    StackTrace? stackTrace,
  }) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = StringBuffer();

      logEntry.writeln('=== 请求错误 ===');
      logEntry.writeln('时间: $timestamp');
      logEntry.writeln('车牌号: ${plateNumber ?? "未指定"}');
      logEntry.writeln('URL: $url');
      logEntry.writeln('错误类型: ${error.runtimeType}');
      logEntry.writeln('错误信息: $error');

      if (error is DioException) {
        logEntry.writeln('Dio错误类型: ${error.type}');
        logEntry.writeln('Dio错误消息: ${error.message}');

        if (error.response != null) {
          logEntry.writeln('响应状态码: ${error.response!.statusCode}');
          logEntry.writeln('响应数据: ${_formatJson(error.response!.data)}');
          logEntry.writeln('响应头: ${_formatJson(error.response!.headers.map)}');
        }

        logEntry.writeln('请求选项:');
        logEntry.writeln('  方法: ${error.requestOptions.method}');
        logEntry.writeln('  URL: ${error.requestOptions.uri}');
        logEntry.writeln('  连接超时: ${error.requestOptions.connectTimeout}ms');
        logEntry.writeln('  接收超时: ${error.requestOptions.receiveTimeout}ms');
        logEntry.writeln('  发送超时: ${error.requestOptions.sendTimeout}ms');
      }

      // 仅在Debug模式下记录详细堆栈跟踪
      if (kDebugMode && stackTrace != null) {
        logEntry.writeln('堆栈跟踪:');
        logEntry.writeln(stackTrace.toString());
      }

      logEntry.writeln('================\n');

      // 仅在Debug模式下写入文件
      if (kDebugMode) {
        await _writeLog(logEntry.toString());
      }

      // 错误信息始终输出到控制台（简化版本）
      if (kDebugMode) {
        Logger.instance.error(logEntry.toString(), 'ApiLogger');
      } else {
        // Release模式下只输出简化的错误信息
        Logger.instance.error('API请求错误 - 车牌: ${plateNumber ?? "未知"}, URL: $url, 错误: ${error.toString()}', 'ApiLogger');
      }
    } catch (e) {
      Logger.instance.error('记录错误日志失败: $e', 'ApiLogger');
    }
  }
  
  /// 获取日志文件路径
  static String? getLogFilePath() {
    return _logFile?.path;
  }
  
  /// 清空日志文件
  static Future<void> clearLogs() async {
    // 仅在Debug模式下可用
    if (!kDebugMode) return;

    try {
      if (_logFile != null && await _logFile!.exists()) {
        await _logFile!.writeAsString('');
        await _writeLog('=== 日志已清空 ===');
        await _writeLog('时间: ${DateTime.now().toIso8601String()}\n');
      }
    } catch (e) {
      Logger.instance.error('清空日志失败: $e', 'ApiLogger');
    }
  }

  /// 读取日志内容
  static Future<String> readLogs() async {
    // 仅在Debug模式下可用
    if (!kDebugMode) {
      return 'API日志功能仅在Debug模式下可用';
    }

    try {
      if (_logFile != null && await _logFile!.exists()) {
        return await _logFile!.readAsString();
      }
      return '日志文件不存在';
    } catch (e) {
      return '读取日志失败: $e';
    }
  }
  
  /// 写入日志到文件
  static Future<void> _writeLog(String message) async {
    // 仅在Debug模式下写入文件
    if (!kDebugMode) return;

    try {
      if (_logFile != null) {
        await _logFile!.writeAsString(
          message,
          mode: FileMode.append,
          flush: true,
        );
      }
    } catch (e) {
      Logger.instance.error('写入日志文件失败: $e', 'ApiLogger');
    }
  }
  
  /// 格式化JSON数据
  static String _formatJson(dynamic data) {
    try {
      if (data == null) return 'null';
      
      // 如果已经是字符串，直接返回
      if (data is String) return data;
      
      // 尝试格式化为JSON
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(data);
    } catch (e) {
      return data.toString();
    }
  }
}
