import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

/// 人性化行为模拟器
/// 实现随机延迟、请求序列化和速率限制以模拟真实用户行为
class HumanBehavior {
  static HumanBehavior? _instance;
  static HumanBehavior get instance => _instance ??= HumanBehavior._();
  
  final Random _random = Random();
  final Map<String, DateTime> _lastRequestTimes = {};
  final Map<String, int> _requestCounts = {};
  final Map<String, Timer> _cooldownTimers = {};
  
  // 配置参数
  static const int _minDelayMs = 800;      // 最小延迟
  static const int _maxDelayMs = 3000;     // 最大延迟
  static const int _maxRequestsPerMinute = 20; // 每分钟最大请求数
  static const int _burstLimit = 5;        // 突发请求限制
  static const int _cooldownSeconds = 30;  // 冷却时间
  
  HumanBehavior._();
  
  /// 执行带有人性化延迟的操作
  Future<T> executeWithDelay<T>(
    Future<T> Function() operation, {
    String? operationId,
    int? minDelayMs,
    int? maxDelayMs,
    bool respectRateLimit = true,
  }) async {
    final id = operationId ?? 'default';
    
    // 检查速率限制
    if (respectRateLimit && !await _checkRateLimit(id)) {
      throw Exception('请求频率过高，请稍后再试');
    }
    
    // 计算延迟时间
    final delay = _calculateDelay(
      minDelayMs ?? _minDelayMs,
      maxDelayMs ?? _maxDelayMs,
    );
    
    Logger.instance.debug('执行操作前延迟 ${delay}ms (操作ID: $id)', 'HumanBehavior');
    
    // 执行延迟
    await Future.delayed(Duration(milliseconds: delay));
    
    // 记录请求时间
    _recordRequest(id);
    
    // 执行操作
    try {
      final result = await operation();
      Logger.instance.debug('操作执行成功 (操作ID: $id)', 'HumanBehavior');
      return result;
    } catch (e) {
      Logger.instance.error('操作执行失败 (操作ID: $id): $e', 'HumanBehavior');
      rethrow;
    }
  }
  
  /// 批量执行操作（带有人性化间隔）
  Future<List<T>> executeBatch<T>(
    List<Future<T> Function()> operations, {
    String? batchId,
    int? concurrency,
    Function(int completed, int total)? onProgress,
  }) async {
    final id = batchId ?? 'batch_${DateTime.now().millisecondsSinceEpoch}';
    final actualConcurrency = (concurrency ?? 2).clamp(1, 3);
    
    Logger.instance.info('开始批量执行 ${operations.length} 个操作，并发数: $actualConcurrency', 'HumanBehavior');
    
    final results = <T>[];
    final queue = Queue<Future<T> Function()>.from(operations);
    int completed = 0;

    final completer = Completer<List<T>>();

    // 处理下一个操作
    void processNext() async {
      if (queue.isEmpty) {
        if (completed == operations.length) {
          completer.complete(results);
        }
        return;
      }

      final operation = queue.removeFirst();
      final operationIndex = operations.length - queue.length - 1;
      
      try {
        // 执行带延迟的操作
        final result = await executeWithDelay(
          operation,
          operationId: '${id}_$operationIndex',
        );
        
        results.add(result);
        completed++;
        
        // 更新进度
        onProgress?.call(completed, operations.length);
        
        // 在操作之间添加额外的随机间隔
        if (queue.isNotEmpty) {
          final extraDelay = _calculateBatchDelay();
          await Future.delayed(Duration(milliseconds: extraDelay));
        }
        
        // 处理下一个操作
        processNext();
      } catch (e) {
        Logger.instance.error('批量操作中的单个操作失败: $e', 'HumanBehavior');
        completed++;
        onProgress?.call(completed, operations.length);
        processNext();
      }
    }
    
    // 启动并发任务
    for (int i = 0; i < actualConcurrency; i++) {
      processNext();
    }
    
    return completer.future;
  }
  
  /// 检查速率限制
  Future<bool> _checkRateLimit(String operationId) async {
    final now = DateTime.now();
    final lastRequest = _lastRequestTimes[operationId];
    final requestCount = _requestCounts[operationId] ?? 0;
    
    // 检查是否在冷却期
    if (_cooldownTimers.containsKey(operationId)) {
      Logger.instance.warning('操作 $operationId 正在冷却期', 'HumanBehavior');
      return false;
    }
    
    // 检查每分钟请求限制
    if (lastRequest != null) {
      final timeSinceLastRequest = now.difference(lastRequest);
      
      // 如果在同一分钟内
      if (timeSinceLastRequest.inMinutes == 0) {
        if (requestCount >= _maxRequestsPerMinute) {
          Logger.instance.warning('操作 $operationId 超过每分钟请求限制', 'HumanBehavior');
          _startCooldown(operationId);
          return false;
        }
        
        // 检查突发请求限制
        if (requestCount >= _burstLimit && timeSinceLastRequest.inSeconds < 10) {
          Logger.instance.warning('操作 $operationId 触发突发请求限制', 'HumanBehavior');
          _startCooldown(operationId);
          return false;
        }
      } else {
        // 重置计数器（新的一分钟）
        _requestCounts[operationId] = 0;
      }
      
      // 检查最小请求间隔
      if (timeSinceLastRequest.inMilliseconds < _minDelayMs) {
        final waitTime = _minDelayMs - timeSinceLastRequest.inMilliseconds;
        Logger.instance.debug('等待最小请求间隔: ${waitTime}ms', 'HumanBehavior');
        await Future.delayed(Duration(milliseconds: waitTime));
      }
    }
    
    return true;
  }
  
  /// 记录请求
  void _recordRequest(String operationId) {
    final now = DateTime.now();
    _lastRequestTimes[operationId] = now;
    _requestCounts[operationId] = (_requestCounts[operationId] ?? 0) + 1;
  }
  
  /// 开始冷却期
  void _startCooldown(String operationId) {
    _cooldownTimers[operationId] = Timer(
      Duration(seconds: _cooldownSeconds),
      () {
        _cooldownTimers.remove(operationId);
        _requestCounts[operationId] = 0;
        Logger.instance.info('操作 $operationId 冷却期结束', 'HumanBehavior');
      },
    );
    
    Logger.instance.info('操作 $operationId 进入冷却期 ${_cooldownSeconds}秒', 'HumanBehavior');
  }
  
  /// 计算随机延迟
  int _calculateDelay(int minMs, int maxMs) {
    // 使用正态分布生成更自然的延迟
    final mean = (minMs + maxMs) / 2;
    final stdDev = (maxMs - minMs) / 6; // 99.7%的值在3个标准差内
    
    // 生成正态分布的随机数
    final u1 = _random.nextDouble();
    final u2 = _random.nextDouble();
    final z0 = sqrt(-2 * log(u1)) * cos(2 * pi * u2);
    
    final delay = (mean + stdDev * z0).round().clamp(minMs, maxMs);
    
    return delay;
  }
  
  /// 计算批量操作间的延迟
  int _calculateBatchDelay() {
    // 批量操作间的额外延迟，模拟用户思考时间
    final baseDelay = 200;
    final randomDelay = _random.nextInt(800);
    return baseDelay + randomDelay;
  }
  
  /// 模拟用户输入延迟
  Future<void> simulateTypingDelay(String text) async {
    // 模拟打字速度：每个字符50-150ms
    final delayPerChar = 50 + _random.nextInt(100);
    final totalDelay = text.length * delayPerChar;
    
    Logger.instance.debug('模拟输入延迟: ${totalDelay}ms (${text.length}个字符)', 'HumanBehavior');
    await Future.delayed(Duration(milliseconds: totalDelay));
  }
  
  /// 模拟页面加载等待
  Future<void> simulatePageLoadWait() async {
    // 模拟用户等待页面加载的时间
    final delay = 1000 + _random.nextInt(2000); // 1-3秒
    Logger.instance.debug('模拟页面加载等待: ${delay}ms', 'HumanBehavior');
    await Future.delayed(Duration(milliseconds: delay));
  }
  
  /// 获取操作统计信息
  Map<String, dynamic> getStats() {
    final stats = <String, dynamic>{};
    
    for (final operationId in _lastRequestTimes.keys) {
      stats[operationId] = {
        'lastRequest': _lastRequestTimes[operationId]?.toIso8601String(),
        'requestCount': _requestCounts[operationId] ?? 0,
        'inCooldown': _cooldownTimers.containsKey(operationId),
      };
    }
    
    return stats;
  }
  
  /// 重置所有统计信息
  void reset() {
    _lastRequestTimes.clear();
    _requestCounts.clear();
    
    // 取消所有冷却计时器
    for (final timer in _cooldownTimers.values) {
      timer.cancel();
    }
    _cooldownTimers.clear();
    
    Logger.instance.info('人性化行为统计信息已重置', 'HumanBehavior');
  }
  
  /// 检查操作是否可以执行
  bool canExecute(String operationId) {
    return !_cooldownTimers.containsKey(operationId);
  }
  
  /// 获取下次可执行时间
  DateTime? getNextExecutionTime(String operationId) {
    final lastRequest = _lastRequestTimes[operationId];
    if (lastRequest == null) return DateTime.now();
    
    if (_cooldownTimers.containsKey(operationId)) {
      return lastRequest.add(Duration(seconds: _cooldownSeconds));
    }
    
    return lastRequest.add(Duration(milliseconds: _minDelayMs));
  }
}
