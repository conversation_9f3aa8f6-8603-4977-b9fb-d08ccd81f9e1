import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';
import '../models/plate_number.dart';
import '../models/search_record.dart';
import '../services/api_service.dart';
import '../config/history_config.dart';
import '../utils/plate_generator.dart';
import '../enums/vehicle_type.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  late final Database _database;
  final _store = stringMapStoreFactory.store('search_records');
  final _apiService = ApiService();

  Future<void> init() async {
    final appDir = await getApplicationDocumentsDirectory();
    final dbPath = join(appDir.path, 'myplates-vic.db');
    _database = await databaseFactoryIo.openDatabase(dbPath);
  }

  Future<List<SearchRecord>> getRecords({
    required int limit,
    required int offset,
    HistoryFilterOption filter = HistoryFilterOption.all,
    HistorySortOption sort = HistorySortOption.dateDesc,
  }) async {
    // Build filter conditions
    Filter? filterCondition;
    switch (filter) {
      case HistoryFilterOption.all:
        filterCondition = null;
        break;
      case HistoryFilterOption.custom:
        filterCondition = Filter.equals('plateType', 'Custom');
        break;
      case HistoryFilterOption.personalised:
        filterCondition = Filter.equals('plateType', 'Personalised');
        break;
      case HistoryFilterOption.available:
        filterCondition = Filter.equals('isAvailable', true);
        break;
      case HistoryFilterOption.unavailable:
        filterCondition = Filter.equals('isAvailable', false);
        break;
    }

    // Build sort orders
    List<SortOrder> sortOrders;
    switch (sort) {
      case HistorySortOption.dateDesc:
        sortOrders = [SortOrder('searchTime', false)];
        break;
      case HistorySortOption.dateAsc:
        sortOrders = [SortOrder('searchTime', true)];
        break;
      case HistorySortOption.plateNumber:
        sortOrders = [SortOrder('plateNumber', true)];
        break;
      case HistorySortOption.availableFirst:
        sortOrders = [SortOrder('isAvailable', false), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.unavailableFirst:
        sortOrders = [SortOrder('isAvailable', true), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.typePersonalisedFirst:
        sortOrders = [SortOrder('plateType', false), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.typeCustomFirst:
        sortOrders = [SortOrder('plateType', true), SortOrder('searchTime', false)];
        break;
    }

    final finder = Finder(
      filter: filterCondition,
      sortOrders: sortOrders,
      limit: limit,
      offset: offset,
    );

    final records = await _store.find(_database, finder: finder);
    return records
        .map((record) => SearchRecord.fromJson(record.value))
        .toList();
  }

  /// Get total count of records with optional filtering
  Future<int> getRecordsCount({
    HistoryFilterOption filter = HistoryFilterOption.all,
  }) async {
    // Build filter conditions
    Filter? filterCondition;
    switch (filter) {
      case HistoryFilterOption.all:
        filterCondition = null;
        break;
      case HistoryFilterOption.custom:
        filterCondition = Filter.equals('plateType', 'Custom');
        break;
      case HistoryFilterOption.personalised:
        filterCondition = Filter.equals('plateType', 'Personalised');
        break;
      case HistoryFilterOption.available:
        filterCondition = Filter.equals('isAvailable', true);
        break;
      case HistoryFilterOption.unavailable:
        filterCondition = Filter.equals('isAvailable', false);
        break;
    }

    final finder = Finder(filter: filterCondition);
    final records = await _store.find(_database, finder: finder);
    return records.length;
  }

  Future<SearchRecord> refreshRecord(SearchRecord record) async {
    // 创建PlateNumber对象用于API查询
    final plate = PlateNumber(
      number: record.plateNumber,
      type: record.plateType, // Use the stored plate type
    );

    // 调用API服务查询可用性（使用默认车辆类型）
    final results = await _apiService.checkPlatesAvailability(
      plates: [plate],
      concurrency: 1,
      vehicleType: VehicleType.car, // 历史记录刷新使用默认车辆类型
      onProgress: (completed, total) {
        // 单个查询不需要处理进度
      },
      // 单个查询不需要实时保存回调和取消功能
    );

    if (results.isEmpty) {
      throw Exception('Failed to check plate availability');
    }

    final updatedPlate = results.first;

    // 更新记录
    final updatedRecord = record.copyWith(
      isAvailable: updatedPlate.isAvailable ?? false,
      searchTime: DateTime.now(),
    );

    // 保存到数据库
    await _store.record(record.id).update(_database, updatedRecord.toJson());
    return updatedRecord;
  }

  Future<void> addRecord(SearchRecord record) async {
    await _store.record(record.id).add(_database, record.toJson());
  }

  Future<void> deleteRecord(String id) async {
    await _store.record(id).delete(_database);
  }

  /// 切换记录的收藏状态
  Future<SearchRecord> toggleFavorite(SearchRecord record) async {
    final updatedRecord = record.copyWith(isFavorite: !record.isFavorite);
    await _store.record(record.id).update(_database, updatedRecord.toJson());
    return updatedRecord;
  }

  /// 获取收藏的记录
  Future<List<SearchRecord>> getFavoriteRecords({
    required int limit,
    required int offset,
    HistorySortOption sort = HistorySortOption.dateDesc,
  }) async {
    // Build sort orders
    List<SortOrder> sortOrders;
    switch (sort) {
      case HistorySortOption.dateDesc:
        sortOrders = [SortOrder('searchTime', false)];
        break;
      case HistorySortOption.dateAsc:
        sortOrders = [SortOrder('searchTime', true)];
        break;
      case HistorySortOption.plateNumber:
        sortOrders = [SortOrder('plateNumber', true)];
        break;
      case HistorySortOption.availableFirst:
        sortOrders = [SortOrder('isAvailable', false), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.unavailableFirst:
        sortOrders = [SortOrder('isAvailable', true), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.typePersonalisedFirst:
        sortOrders = [SortOrder('plateType', false), SortOrder('searchTime', false)];
        break;
      case HistorySortOption.typeCustomFirst:
        sortOrders = [SortOrder('plateType', true), SortOrder('searchTime', false)];
        break;
    }

    final finder = Finder(
      filter: Filter.equals('isFavorite', true),
      sortOrders: sortOrders,
      limit: limit,
      offset: offset,
    );

    final records = await _store.find(_database, finder: finder);
    return records
        .map((record) => SearchRecord.fromJson(record.value))
        .toList();
  }

  /// 获取收藏记录的总数
  Future<int> getFavoriteRecordsCount() async {
    final finder = Finder(filter: Filter.equals('isFavorite', true));
    final records = await _store.find(_database, finder: finder);
    return records.length;
  }

  /// 保存查询记录到数据库 - 如果已存在则更新，否则创建新记录
  Future<void> saveSearchRecord(PlateNumber plate, [String? vehicleType]) async {
    // 检查是否已存在相同的车牌号码
    final finder = Finder(
      filter: Filter.equals('plateNumber', plate.number),
    );

    final existingRecords = await _store.find(_database, finder: finder);
    
    if (existingRecords.isNotEmpty) {
      // 如果存在，更新现有记录
      final existingRecord = SearchRecord.fromJson(existingRecords.first.value);
      final updatedRecord = existingRecord.copyWith(
        isAvailable: plate.isAvailable ?? false,
        searchTime: plate.queryTime ?? DateTime.now(),
        plateType: plate.type, // 更新车牌类型
        vehicleType: vehicleType ?? existingRecord.vehicleType, // 保持现有车辆类型（如果未提供）
      );
      await _store.record(existingRecord.id).update(_database, updatedRecord.toJson());
    } else {
      // 如果不存在，创建新记录
      final record = SearchRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        plateNumber: plate.number,
        isAvailable: plate.isAvailable ?? false,
        searchTime: plate.queryTime ?? DateTime.now(),
        plateType: plate.type,
        vehicleType: vehicleType // Can be null
      );
      await addRecord(record);
    }
  }

  /// 强制更新查询记录（已弃用 - 请使用 saveSearchRecord）
  @Deprecated('Use saveSearchRecord instead, which automatically handles updates')
  Future<void> updateSearchRecord(PlateNumber plate, [String? vehicleType]) async {
    // 直接调用 saveSearchRecord，它已经包含了更新逻辑
    await saveSearchRecord(plate, vehicleType);
  }

  /// 获取所有查询记录
  Future<List<PlateNumber>> getAllRecords() async {
    final records = await _store.find(_database);

    return records.map((record) {
      final data = record.value;
      return PlateNumber(
        number: data['plateNumber'] as String,
        type: data['plateType'] as String? ?? PlateGenerator.getPlateType(data['plateNumber'] as String), // Use stored type or determine it
        isAvailable: data['isAvailable'] as bool?,
        queryTime: data['searchTime'] != null
            ? DateTime.parse(data['searchTime'] as String)
            : null,
      );
    }).toList();
  }

  /// 检查车牌号码是否已存在于数据库中
  Future<bool> plateNumberExists(String plateNumber) async {
    final finder = Finder(
      filter: Filter.equals('plateNumber', plateNumber),
    );
    final records = await _store.find(_database, finder: finder);
    return records.isNotEmpty;
  }

  /// 根据车牌号码获取搜索记录
  Future<SearchRecord?> getRecordByPlateNumber(String plateNumber) async {
    final finder = Finder(
      filter: Filter.equals('plateNumber', plateNumber),
    );
    final records = await _store.find(_database, finder: finder);
    if (records.isNotEmpty) {
      return SearchRecord.fromJson(records.first.value);
    }
    return null;
  }
}
