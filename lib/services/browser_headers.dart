import 'dart:math';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../utils/logger.dart';

/// 浏览器请求头管理器
/// 生成真实的浏览器请求头以绕过机器人检测
class BrowserHeaders {
  static BrowserHeaders? _instance;
  static BrowserHeaders get instance => _instance ??= BrowserHeaders._();
  
  final Random _random = Random();
  String? _cachedFingerprint;
  Map<String, String>? _cachedHeaders;
  DateTime? _lastHeaderUpdate;
  
  // 常见的浏览器版本
  static const List<String> _chromeVersions = [
    '120.0.0.0', '119.0.0.0', '1********', '*********'
  ];
  
  static const List<String> _firefoxVersions = [
    '121.0', '120.0', '119.0', '118.0'
  ];
  
  static const List<String> _safariVersions = [
    '17.1', '17.0', '16.6', '16.5'
  ];
  
  // 常见的语言设置
  static const List<String> _languages = [
    'zh-CN,zh;q=0.9,en;q=0.8',
    'en-US,en;q=0.9',
    'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
    'en-AU,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
  ];
  
  // 常见的编码设置
  static const List<String> _encodings = [
    'gzip, deflate, br',
    'gzip, deflate',
    'gzip, deflate, br, zstd',
  ];
  
  BrowserHeaders._();
  
  /// 获取完整的浏览器请求头
  Future<Map<String, String>> getBrowserHeaders({
    String? referer,
    bool forceRefresh = false,
  }) async {
    // 如果缓存的请求头仍然有效且不强制刷新，则返回缓存
    if (!forceRefresh && 
        _cachedHeaders != null && 
        _lastHeaderUpdate != null &&
        DateTime.now().difference(_lastHeaderUpdate!).inMinutes < 30) {
      
      // 更新动态字段
      final headers = Map<String, String>.from(_cachedHeaders!);
      if (referer != null) {
        headers['Referer'] = referer;
      }
      headers['X-Requested-With'] = 'XMLHttpRequest';
      headers['Cache-Control'] = 'no-cache';
      headers['Pragma'] = 'no-cache';
      
      return headers;
    }
    
    // 生成新的请求头
    final headers = await _generateBrowserHeaders(referer: referer);
    
    // 缓存请求头（除了动态字段）
    _cachedHeaders = Map<String, String>.from(headers);
    _cachedHeaders!.remove('Referer');
    _cachedHeaders!.remove('X-Requested-With');
    _cachedHeaders!.remove('Cache-Control');
    _cachedHeaders!.remove('Pragma');
    _lastHeaderUpdate = DateTime.now();
    
    return headers;
  }
  
  /// 生成浏览器请求头
  Future<Map<String, String>> _generateBrowserHeaders({String? referer}) async {
    final userAgent = await _generateUserAgent();
    final fingerprint = await _generateFingerprint();
    
    final headers = <String, String>{
      // 基本请求头
      'User-Agent': userAgent,
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': _getRandomLanguage(),
      'Accept-Encoding': _getRandomEncoding(),
      
      // 浏览器特征
      'DNT': '1', // Do Not Track
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      
      // 安全相关
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'Sec-CH-UA': _generateSecChUa(),
      'Sec-CH-UA-Mobile': _isMobile() ? '?1' : '?0',
      'Sec-CH-UA-Platform': '"${_getPlatformName()}"',
      
      // 缓存控制
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      
      // 请求标识
      'X-Requested-With': 'XMLHttpRequest',
      
      // 自定义指纹
      'X-Browser-Fingerprint': fingerprint,
    };
    
    // 添加Referer（如果提供）
    if (referer != null) {
      headers['Referer'] = referer;
    }
    
    // 根据平台添加特定请求头
    if (Platform.isAndroid) {
      headers['X-Requested-With'] = 'com.android.browser';
    } else if (Platform.isIOS) {
      headers['X-Requested-With'] = 'Safari';
    }
    
    Logger.instance.debug('生成浏览器请求头: ${headers.length} 个字段', 'BrowserHeaders');
    
    return headers;
  }
  
  /// 生成用户代理字符串
  Future<String> _generateUserAgent() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (kIsWeb) {
        return _getRandomChromeUserAgent();
      }
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return _getAndroidUserAgent(androidInfo);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return _getIOSUserAgent(iosInfo);
      } else if (Platform.isWindows) {
        return _getRandomChromeUserAgent();
      } else if (Platform.isMacOS) {
        return _getRandomSafariUserAgent();
      } else if (Platform.isLinux) {
        return _getRandomFirefoxUserAgent();
      }
    } catch (e) {
      Logger.instance.warning('获取设备信息失败，使用随机用户代理: $e', 'BrowserHeaders');
    }
    
    // 默认返回随机Chrome用户代理
    return _getRandomChromeUserAgent();
  }
  
  /// 生成随机Chrome用户代理
  String _getRandomChromeUserAgent() {
    final version = _chromeVersions[_random.nextInt(_chromeVersions.length)];
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$version Safari/537.36';
  }
  
  /// 生成随机Firefox用户代理
  String _getRandomFirefoxUserAgent() {
    final version = _firefoxVersions[_random.nextInt(_firefoxVersions.length)];
    return 'Mozilla/5.0 (X11; Linux x86_64; rv:$version) Gecko/20100101 Firefox/$version';
  }
  
  /// 生成随机Safari用户代理
  String _getRandomSafariUserAgent() {
    final version = _safariVersions[_random.nextInt(_safariVersions.length)];
    return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/$version Safari/605.1.15';
  }
  
  /// 生成Android用户代理
  String _getAndroidUserAgent(AndroidDeviceInfo androidInfo) {
    final version = androidInfo.version.release;
    final model = androidInfo.model;
    final brand = androidInfo.brand;
    final chromeVersion = _chromeVersions[_random.nextInt(_chromeVersions.length)];
    
    return 'Mozilla/5.0 (Linux; Android $version; $brand $model) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/$chromeVersion Mobile Safari/537.36';
  }
  
  /// 生成iOS用户代理
  String _getIOSUserAgent(IosDeviceInfo iosInfo) {
    final version = iosInfo.systemVersion.replaceAll('.', '_');
    final model = iosInfo.model;
    final safariVersion = _safariVersions[_random.nextInt(_safariVersions.length)];
    
    return 'Mozilla/5.0 ($model; CPU OS $version like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/$safariVersion Mobile/15E148 Safari/604.1';
  }
  
  /// 生成Sec-CH-UA请求头
  String _generateSecChUa() {
    final chromeVersion = _chromeVersions[_random.nextInt(_chromeVersions.length)];
    final majorVersion = chromeVersion.split('.')[0];
    
    return '"Chromium";v="$majorVersion", "Google Chrome";v="$majorVersion", "Not=A?Brand";v="99"';
  }
  
  /// 获取随机语言设置
  String _getRandomLanguage() {
    return _languages[_random.nextInt(_languages.length)];
  }
  
  /// 获取随机编码设置
  String _getRandomEncoding() {
    return _encodings[_random.nextInt(_encodings.length)];
  }
  
  /// 判断是否为移动设备
  bool _isMobile() {
    return Platform.isAndroid || Platform.isIOS;
  }
  
  /// 获取平台名称
  String _getPlatformName() {
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    return 'Unknown';
  }
  
  /// 生成浏览器指纹
  Future<String> _generateFingerprint() async {
    if (_cachedFingerprint != null) {
      return _cachedFingerprint!;
    }
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      final buffer = StringBuffer();
      
      // 添加设备信息
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        buffer.write('${androidInfo.brand}-${androidInfo.model}-${androidInfo.version.release}');
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        buffer.write('${iosInfo.model}-${iosInfo.systemVersion}');
      } else {
        buffer.write('${Platform.operatingSystem}-${Platform.operatingSystemVersion}');
      }
      
      // 添加时间戳（小时级别，确保一定时间内指纹一致）
      final hourTimestamp = DateTime.now().millisecondsSinceEpoch ~/ (1000 * 60 * 60);
      buffer.write('-$hourTimestamp');
      
      // 生成MD5哈希
      final bytes = utf8.encode(buffer.toString());
      final digest = md5.convert(bytes);
      
      _cachedFingerprint = digest.toString();
      Logger.instance.debug('生成浏览器指纹: $_cachedFingerprint', 'BrowserHeaders');
      
      return _cachedFingerprint!;
    } catch (e) {
      Logger.instance.error('生成浏览器指纹失败: $e', 'BrowserHeaders');
      // 生成随机指纹作为回退
      final randomBytes = List.generate(16, (index) => _random.nextInt(256));
      _cachedFingerprint = randomBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
      return _cachedFingerprint!;
    }
  }
  
  /// 清除缓存的请求头和指纹
  void clearCache() {
    _cachedHeaders = null;
    _cachedFingerprint = null;
    _lastHeaderUpdate = null;
    Logger.instance.debug('已清除浏览器请求头缓存', 'BrowserHeaders');
  }
}
