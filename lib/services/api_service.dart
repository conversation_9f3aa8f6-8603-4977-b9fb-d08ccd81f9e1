import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/plate_number.dart';
import '../config/app_config.dart';
import '../enums/vehicle_type.dart';
import 'api_logger.dart';
import '../utils/logger.dart';

class ApiService {
  final Dio _dio = Dio();
  final int _maxRetries = 3;

  ApiService() {
    // 配置Dio
    _configureDio();
  }

  /// 配置Dio实例
  void _configureDio() {
    // 设置基础配置
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // 仅在Debug模式下添加详细日志拦截器
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        error: true,
        logPrint: (obj) => Logger.instance.debug('[Dio] $obj', 'ApiService'),
      ));
    }
  }

  /// 并发查询多个车牌号码
  ///
  /// [plates] 要查询的车牌号码列表
  /// [concurrency] 并发数量 (1-3)
  /// [vehicleType] 车辆类型
  /// [onProgress] 进度回调函数
  /// [onPlateCompleted] 单个车牌完成时的回调函数，用于实时保存
  /// [shouldCancel] 检查是否应该取消查询的函数
  Future<List<PlateNumber>> checkPlatesAvailability({
    required List<PlateNumber> plates,
    required int concurrency,
    required VehicleType vehicleType,
    required Function(int completed, int total) onProgress,
    Function(PlateNumber plate)? onPlateCompleted,
    bool Function()? shouldCancel,
  }) async {
    Logger.instance.info('开始查询 ${plates.length} 个车牌号码', 'ApiService');

    // 验证并发数
    final int actualConcurrency = concurrency.clamp(1, 3);
    Logger.instance.info('实际并发数: $actualConcurrency', 'ApiService');

    // 创建结果列表
    final List<PlateNumber> results = [];

    // 创建查询队列
    final queue = Queue<PlateNumber>.from(plates);
    int completed = 0;

    // 使用Completer来等待所有任务完成
    final completer = Completer<List<PlateNumber>>();

    // 处理下一个车牌
    void processNext() async {
      // 检查是否应该取消查询
      if (shouldCancel?.call() == true) {
        Logger.instance.info('查询被用户取消', 'ApiService');
        completer.complete(results);
        return;
      }

      if (queue.isEmpty) {
        // 如果队列为空且所有任务已完成，则完成Future
        if (completed == plates.length) {
          Logger.instance.info('所有查询任务已完成', 'ApiService');
          completer.complete(results);
        }
        return;
      }

      final plate = queue.removeFirst();
      Logger.instance.debug('正在查询车牌: ${plate.number}', 'ApiService');

      try {
        // 查询车牌可用性
        final updatedPlate = await _checkPlateWithRetry(plate, vehicleType, shouldCancel);
        results.add(updatedPlate);
        Logger.instance.debug('车牌 ${plate.number} 查询结果: ${updatedPlate.isAvailable}', 'ApiService');

        // 调用单个车牌完成回调（实时保存）
        if (onPlateCompleted != null) {
          try {
            await onPlateCompleted(updatedPlate);
          } catch (e) {
            Logger.instance.error('保存车牌 ${plate.number} 到数据库时发生错误: $e', 'ApiService');
          }
        }

        // 更新进度
        completed++;
        onProgress(completed, plates.length);

        // 处理下一个
        processNext();
      } catch (e) {
        Logger.instance.error('查询车牌 ${plate.number} 时发生错误: $e', 'ApiService');
        // 处理错误，将失败的查询添加回队列或标记为失败
        final failedPlate = plate.copyWith(
          isAvailable: null,
          queryTime: DateTime.now(),
        );
        results.add(failedPlate);

        // 即使失败也要调用回调保存记录
        if (onPlateCompleted != null) {
          try {
            await onPlateCompleted(failedPlate);
          } catch (e) {
            Logger.instance.error('保存失败车牌 ${plate.number} 到数据库时发生错误: $e', 'ApiService');
          }
        }

        completed++;
        onProgress(completed, plates.length);
        processNext();
      }
    }

    // 启动并发任务
    for (int i = 0; i < actualConcurrency; i++) {
      processNext();
    }

    // 等待所有任务完成
    return completer.future;
  }

  /// 带重试机制的车牌查询
  Future<PlateNumber> _checkPlateWithRetry(PlateNumber plate, VehicleType vehicleType, [bool Function()? shouldCancel]) async {
    int attempts = 0;

    while (attempts < _maxRetries) {
      // 在重试前检查是否应该取消
      if (shouldCancel?.call() == true) {
        Logger.instance.info('查询车牌 ${plate.number} 被取消', 'ApiService');
        throw Exception('Query cancelled by user');
      }

      try {
        // 尝试查询
        return await _checkPlateAvailability(plate, vehicleType);
      } catch (e) {
        attempts++;
        Logger.instance.warning('第 $attempts 次重试查询车牌 ${plate.number}', 'ApiService');

        // 如果达到最大重试次数，抛出异常
        if (attempts >= _maxRetries) {
          Logger.instance.error('达到最大重试次数，放弃查询车牌 ${plate.number}', 'ApiService');
          rethrow;
        }

        // 计算退避时间 (指数增长: 500ms, 1000ms, 2000ms...)
        final backoffMs = (500 * pow(2, attempts - 1)).toInt();
        // 添加随机抖动，避免同时重试
        final jitter = Random().nextInt(200);
        final delayMs = backoffMs + jitter;

        Logger.instance.debug('等待 ${delayMs}ms 后重试', 'ApiService');

        // 在等待期间分段检查取消状态
        const checkInterval = 100; // 每100ms检查一次
        int elapsed = 0;
        while (elapsed < delayMs) {
          if (shouldCancel?.call() == true) {
            Logger.instance.info('在重试等待期间查询被取消', 'ApiService');
            throw Exception('Query cancelled by user');
          }
          final waitTime = (delayMs - elapsed).clamp(0, checkInterval);
          await Future.delayed(Duration(milliseconds: waitTime));
          elapsed += waitTime;
        }
      }
    }

    // 不应该到达这里，但为了类型安全
    throw Exception('Failed to check plate after $_maxRetries attempts');
  }

  /// 单个车牌查询
  Future<PlateNumber> _checkPlateAvailability(PlateNumber plate, VehicleType vehicleType) async {
    final url = AppConfig.apiBaseUrl;
    final startTime = DateTime.now();

    Logger.instance.debug('请求URL: $url', 'ApiService');
    Logger.instance.debug('车辆类型: ${vehicleType.apiValue}', 'ApiService');

    try {
      // 获取动态用户代理
      final userAgent = await AppConfig.getUserAgent();
      Logger.instance.debug('使用用户代理: $userAgent', 'ApiService');

      // 准备请求参数
      final queryParameters = {
        'vehicleType': vehicleType.apiValue,
        'combination': plate,
        'productType': 'Create',
        'isRestyle': false,
        '_': startTime
      };
      final headers = {
        'User-Agent': userAgent,
        'Referer': AppConfig.refererUrl,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      };

      // 记录请求日志
      await ApiLogger.logRequest(
        method: 'GET',
        url: url,
        queryParameters: queryParameters,
        headers: headers,
        plateNumber: plate.number,
      );

      final response = await _dio.get(
        url,
        queryParameters: queryParameters,
        options: Options(
          headers: headers,
          validateStatus: (status) {
            return status != null && (status == 200 || status == 404);
          },
          followRedirects: true,
          maxRedirects: 5,
        ),
      );

      final duration = DateTime.now().difference(startTime);

      Logger.instance.debug('响应状态码: ${response.statusCode}', 'ApiService');
      Logger.instance.debug('响应数据: ${response.data}', 'ApiService');

      // 记录响应日志
      await ApiLogger.logResponse(
        url: url,
        statusCode: response.statusCode,
        headers: response.headers.map,
        data: response.data,
        plateNumber: plate.number,
        duration: duration,
      );

      // 处理响应
      bool? isAvailable;

      // 处理响应数据
      final data = response.data;
      if (data is Map<String, dynamic>) {
        isAvailable = data['plateAvailability']?['available'] ?? false;
      } else {
        Logger.instance.warning('警告: 响应数据格式不正确: ${data.runtimeType}', 'ApiService');
        isAvailable = false;
      }

      // 返回更新后的车牌对象
      return plate.copyWith(
        isAvailable: isAvailable,
        queryTime: DateTime.now(),
      );
    } catch (e) {
      Logger.instance.error('查询出错: $e', 'ApiService');

      // 记录错误日志
      await ApiLogger.logError(
        url: url,
        error: e,
        plateNumber: plate.number,
        stackTrace: StackTrace.current,
      );

      // 处理网络错误或其他异常
      if (e is DioException) {
        Logger.instance.error('Dio错误类型: ${e.type}', 'ApiService');
        Logger.instance.error('Dio错误消息: ${e.message}', 'ApiService');
        Logger.instance.error('请求选项: ${e.requestOptions.uri}', 'ApiService');

        if (e.response != null) {
          Logger.instance.error('错误响应状态码: ${e.response!.statusCode}', 'ApiService');
          Logger.instance.error('错误响应数据: ${e.response!.data}', 'ApiService');
          Logger.instance.error('错误响应头: ${e.response!.headers}', 'ApiService');
        }
      }

      // 重新抛出异常以便重试机制处理
      rethrow;
    }
  }
}
