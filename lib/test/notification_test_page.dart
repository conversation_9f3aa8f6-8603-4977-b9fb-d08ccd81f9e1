import 'package:flutter/material.dart';
import '../utils/notification_manager.dart';

/// 通知管理器测试页面
/// 仅在Debug模式下使用，用于测试通知覆盖功能
class NotificationTestPage extends StatelessWidget {
  const NotificationTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('通知测试页面'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '测试顶部滑出通知功能',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              '通知现在从顶部滑出，出现时从上往下，消失时从下往上。\n点击下面的按钮测试各种通知类型和覆盖功能：',
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                NotificationManager.instance.showInfo(
                  context,
                  '这是第一个通知消息',
                );
              },
              child: const Text('显示信息通知'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                NotificationManager.instance.showSuccess(
                  context,
                  '这是成功通知消息',
                );
              },
              child: const Text('显示成功通知'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                NotificationManager.instance.showWarning(
                  context,
                  '这是警告通知消息',
                );
              },
              child: const Text('显示警告通知'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                NotificationManager.instance.showError(
                  context,
                  '这是错误通知消息',
                );
              },
              child: const Text('显示错误通知'),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // 快速连续触发多个通知
                NotificationManager.instance.showInfo(context, '通知 1');
                Future.delayed(const Duration(milliseconds: 500), () {
                  NotificationManager.instance.showSuccess(context, '通知 2 (应该覆盖通知1)');
                });
                Future.delayed(const Duration(milliseconds: 1000), () {
                  NotificationManager.instance.showWarning(context, '通知 3 (应该覆盖通知2)');
                });
                Future.delayed(const Duration(milliseconds: 1500), () {
                  NotificationManager.instance.showError(context, '通知 4 (应该覆盖通知3)');
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('快速连续通知测试'),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                NotificationManager.instance.dismissAll();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('关闭所有通知'),
            ),
            const SizedBox(height: 24),
            const Text(
              '预期行为：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '• 通知从屏幕顶部滑出（从上往下动画）\n'
              '• 消失时向上滑动退出（从下往上动画）\n'
              '• 新通知立即覆盖正在显示的旧通知\n'
              '• 每个通知都重新开始计时\n'
              '• 不会出现通知排队的情况\n'
              '• 可以点击关闭按钮手动关闭通知\n'
              '• 快速连续测试中，只有最后一个通知完整显示',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
