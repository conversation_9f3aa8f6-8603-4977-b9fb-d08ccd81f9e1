import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'providers/search_provider.dart';
import 'providers/locale_provider.dart';
import 'l10n/app_localizations_delegate.dart';
import 'services/database_service.dart';
import 'services/api_logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化数据库服务
  await DatabaseService().init();

  // 仅在Debug模式下初始化API日志记录器
  if (kDebugMode) {
    await ApiLogger.initialize();
  }

  // 初始化语言设置提供者
  final localeProvider = LocaleProvider();
  await localeProvider.initialize();

  runApp(MyApp(localeProvider: localeProvider));
}

class MyApp extends StatelessWidget {
  final LocaleProvider localeProvider;

  const MyApp({super.key, required this.localeProvider});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProvider.value(value: localeProvider),
      ],
      child: Consumer<LocaleProvider>(
        builder: (context, localeProvider, child) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'MyPlate VIC',
            locale: localeProvider.locale,
            localizationsDelegates: const [
              AppLocalizationsDelegate(),
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'), // 英文
              Locale('zh', 'CN'), // 中文
              Locale('fr', 'FR'), // 法语
              Locale('de', 'DE'), // 德语
              Locale('hi', 'IN'), // 印度语
              Locale('es', 'ES'), // 西班牙语
              Locale('it', 'IT'), // 意大利语
              Locale('ja', 'JP'), // 日语
            ],
            theme: ThemeData(
              // 使用天蓝色作为主题色
              colorScheme: ColorScheme.fromSeed(
                seedColor: Colors.lightBlue,
                brightness: Brightness.light,
              ),
              useMaterial3: true,
              inputDecorationTheme: const InputDecorationTheme(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.lightBlue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}
