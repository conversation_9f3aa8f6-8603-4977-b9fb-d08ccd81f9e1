import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../services/database_service.dart';
import '../models/plate_number.dart';
import '../enums/vehicle_type.dart';
import '../utils/logger.dart';

class SearchProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final DatabaseService _dbService = DatabaseService();

  // 查询状态
  bool _isSearching = false;
  bool _isCancelled = false;
  int _completedQueries = 0;
  int _totalQueries = 0;
  int _availablePlates = 0;

  // Getters
  bool get isSearching => _isSearching;
  bool get isCancelled => _isCancelled;
  int get completedQueries => _completedQueries;
  int get totalQueries => _totalQueries;
  int get availablePlates => _availablePlates;
  double get progress =>
      _totalQueries > 0 ? _completedQueries / _totalQueries : 0.0;

  /// 开始查询车牌
  Future<void> startSearch({
    required List<PlateNumber> plates,
    required int concurrency,
    required VehicleType vehicleType,
    required String vehicleDisplayName,
  }) async {
    // 重置状态
    _isSearching = true;
    _isCancelled = false;
    _completedQueries = 0;
    _totalQueries = plates.length;
    _availablePlates = 0;
    notifyListeners();

    try {
      // 开始并发查询，支持实时保存和终止
      final results = await _apiService.checkPlatesAvailability(
        plates: plates,
        concurrency: concurrency,
        vehicleType: vehicleType,
        onProgress: (completed, total) {
          _completedQueries = completed;
          notifyListeners();
        },
        onPlateCompleted: (plate) async {
          // 每完成一个车牌查询就立即保存到数据库
          await _dbService.saveSearchRecord(plate, vehicleDisplayName);
          // 更新可用车牌数量
          if (plate.isAvailable == true) {
            _availablePlates++;
            notifyListeners();
          }
        },
        shouldCancel: () => _isCancelled,
      );

      // 如果查询被取消，不需要再次保存结果
      if (!_isCancelled) {
        // 统计可用车牌数量（防止遗漏）
        _availablePlates = results.where((p) => p.isAvailable == true).length;
      }
    } catch (e) {
      // 处理错误
      Logger.instance.error('Search error: $e', 'SearchProvider');
    } finally {
      // 更新状态
      _isSearching = false;
      notifyListeners();
    }
  }

  /// 取消查询
  void cancelSearch() {
    if (_isSearching) {
      _isCancelled = true;
      notifyListeners();
      Logger.instance.info('用户取消了查询操作', 'SearchProvider');
    }
  }
}
