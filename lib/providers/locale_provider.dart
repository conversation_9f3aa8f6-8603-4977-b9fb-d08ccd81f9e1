import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  static const String _localeKey = 'selected_locale';
  
  Locale? _locale;
  
  Locale? get locale => _locale;
   /// Initialize the locale provider and load saved locale
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLocaleCode = prefs.getString(_localeKey);

    if (savedLocaleCode != null) {
      switch (savedLocaleCode) {
        case 'zh':
          _locale = const Locale('zh', 'CN');
          break;
        case 'fr':
          _locale = const Locale('fr', 'FR');
          break;
        case 'de':
          _locale = const Locale('de', 'DE');
          break;
        case 'hi':
          _locale = const Locale('hi', 'IN');
          break;
        case 'es':
          _locale = const Locale('es', 'ES');
          break;
        case 'it':
          _locale = const Locale('it', 'IT');
          break;
        case 'ja':
          _locale = const Locale('ja', 'JP');
          break;
        default:
          _locale = const Locale('en', 'US'); // 默认英文
      }
    } else {
      _locale = const Locale('en', 'US'); // 默认英文
    }

    notifyListeners();
  }
  
  /// Switch to Chinese
  Future<void> setChineseLocale() async {
    if (_locale?.languageCode != 'zh') {
      _locale = const Locale('zh', 'CN');
      await _saveLocale('zh');
      notifyListeners();
    }
  }
  
  /// Switch to English
  Future<void> setEnglishLocale() async {
    if (_locale?.languageCode != 'en') {
      _locale = const Locale('en', 'US');
      await _saveLocale('en');
      notifyListeners();
    }
  }

  /// Switch to French
  Future<void> setFrenchLocale() async {
    if (_locale?.languageCode != 'fr') {
      _locale = const Locale('fr', 'FR');
      await _saveLocale('fr');
      notifyListeners();
    }
  }

  /// Switch to German
  Future<void> setGermanLocale() async {
    if (_locale?.languageCode != 'de') {
      _locale = const Locale('de', 'DE');
      await _saveLocale('de');
      notifyListeners();
    }
  }

  /// Switch to Hindi
  Future<void> setHindiLocale() async {
    if (_locale?.languageCode != 'hi') {
      _locale = const Locale('hi', 'IN');
      await _saveLocale('hi');
      notifyListeners();
    }
  }

  /// Switch to Spanish
  Future<void> setSpanishLocale() async {
    if (_locale?.languageCode != 'es') {
      _locale = const Locale('es', 'ES');
      await _saveLocale('es');
      notifyListeners();
    }
  }

  /// Switch to Italian
  Future<void> setItalianLocale() async {
    if (_locale?.languageCode != 'it') {
      _locale = const Locale('it', 'IT');
      await _saveLocale('it');
      notifyListeners();
    }
  }

  /// Switch to Japanese
  Future<void> setJapaneseLocale() async {
    if (_locale?.languageCode != 'ja') {
      _locale = const Locale('ja', 'JP');
      await _saveLocale('ja');
      notifyListeners();
    }
  }
  
  /// Toggle between Chinese and English
  Future<void> toggleLanguage() async {
    if (_locale?.languageCode == 'zh') {
      await setEnglishLocale();
    } else {
      await setChineseLocale();
    }
  }

  /// Get current language display name
  String getCurrentLanguageName() {
    switch (_locale?.languageCode) {
      case 'zh':
        return '中文';
      case 'en':
        return 'English';
      case 'fr':
        return 'Français';
      case 'de':
        return 'Deutsch';
      case 'hi':
        return 'हिन्दी';
      case 'es':
        return 'Español';
      case 'it':
        return 'Italiano';
      case 'ja':
        return '日本語';
      default:
        return 'English'; // 默认英文
    }
  }

  /// Get all available languages
  List<LanguageOption> getAvailableLanguages() {
    return [
      LanguageOption(
        code: 'en',
        name: 'English',
        nativeName: 'English',
        isSelected: _locale?.languageCode == 'en',
      ),
      LanguageOption(
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        isSelected: _locale?.languageCode == 'zh',
      ),
      LanguageOption(
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        isSelected: _locale?.languageCode == 'fr',
      ),
      LanguageOption(
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        isSelected: _locale?.languageCode == 'de',
      ),
      LanguageOption(
        code: 'hi',
        name: 'Hindi',
        nativeName: 'हिन्दी',
        isSelected: _locale?.languageCode == 'hi',
      ),
      LanguageOption(
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        isSelected: _locale?.languageCode == 'es',
      ),
      LanguageOption(
        code: 'it',
        name: 'Italian',
        nativeName: 'Italiano',
        isSelected: _locale?.languageCode == 'it',
      ),
      LanguageOption(
        code: 'ja',
        name: 'Japanese',
        nativeName: '日本語',
        isSelected: _locale?.languageCode == 'ja',
      ),
    ];
  }

  /// Set locale by language code
  Future<void> setLocale(String languageCode) async {
    switch (languageCode) {
      case 'zh':
        await setChineseLocale();
        break;
      case 'en':
        await setEnglishLocale();
        break;
      case 'fr':
        await setFrenchLocale();
        break;
      case 'de':
        await setGermanLocale();
        break;
      case 'hi':
        await setHindiLocale();
        break;
      case 'es':
        await setSpanishLocale();
        break;
      case 'it':
        await setItalianLocale();
        break;
      case 'ja':
        await setJapaneseLocale();
        break;
      default:
        // 如果传入了无效的语言代码，默认设为英文
        await setEnglishLocale();
        break;
    }
  }
  
  /// Save locale to shared preferences
  Future<void> _saveLocale(String localeCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_localeKey, localeCode);
  }
}

/// Language option for the language selection menu
class LanguageOption {
  final String code;
  final String name;
  final String nativeName;
  final bool isSelected;

  const LanguageOption({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.isSelected,
  });
}
