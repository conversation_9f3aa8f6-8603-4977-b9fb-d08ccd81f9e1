import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'History';

  @override
  String get keywordLabel => 'Keyword (Optional)';

  @override
  String get keywordHint => 'Enter keyword or leave empty for all combinations';

  @override
  String get modeLabel => 'Mode';

  @override
  String get vehicleTypeLabel => 'Vehicle Type';

  @override
  String get digitsLabel => 'Select Digits';

  @override
  String get generatePlatesButton => 'Generate Plate Combinations';

  @override
  String get enterKeywordError => 'Please enter a keyword';

  @override
  String get selectDigitsError => 'Please select number of digits';

  @override
  String get selectDigitsHint => 'Please select number of digits';

  @override
  String get searchingText => 'Searching...';

  @override
  String get terminatingText => 'Terminating search...';

  @override
  String get searchCompleteText => 'Search complete! Found {} available plates';

  @override
  String get searchTerminatedText => 'Search terminated! Found {} available plates';

  @override
  String get confirmTerminateTitle => 'Confirm Termination';

  @override
  String get confirmTerminateContent => 'Are you sure you want to terminate the current search? Already queried results will be preserved.';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get confirmTerminateButton => 'Confirm Termination';

  // Keyword modes
  @override
  String get keywordModePrefix => 'Prefix';

  @override
  String get keywordModeSuffix => 'Suffix';

  @override
  String get keywordModeAnywhere => 'All';

  @override
  String get keywordModePrefixDesc => 'Keyword appears at the beginning';

  @override
  String get keywordModeSuffixDesc => 'Keyword appears at the end';

  @override
  String get keywordModeAnywhereDesc => 'Keyword can appear begining or end';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'Car';

  @override
  String get vehicleTypeMotorcycle => 'Motorcycle';

  // Plate modal
  @override
  String get plateModalTitle => 'Generated Plate Combinations';

  @override
  String get plateModalLoading => 'Generating plate combinations...';

  @override
  String get plateModalError => 'Error generating plate combinations';

  @override
  String get plateModalRetry => 'Retry';

  @override
  String get plateModalSelectAll => 'Select All';

  @override
  String get plateModalDeselectAll => 'Deselect All';

  @override
  String get plateModalStartQuery => 'Start Query';

  @override
  String get plateModalClose => 'Close';

  @override
  String get plateModalSelected => '{} selected';

  @override
  String get plateModalMaxSelection => 'Maximum {} plates can be selected';

  @override
  String get plateModalConcurrency => 'Concurrency';

  @override
  String get plateModalCopiedToClipboard => 'Copied to clipboard';

  @override
  String get plateModalGenerationFailed => 'Failed to generate plates';

  @override
  String get plateModalNoPlatesWithFilter => 'No plates match current filters';

  @override
  String get plateModalCurrentPage => 'Current Page';

  @override
  String get plateModalNoHistory => 'No History';

  @override
  String get plateModalUnavailable => 'Unavailable';

  @override
  String get plateModalClearSelection => 'Clear Selection';

  @override
  String get plateModalFilter => 'Filter';

  @override
  String get plateModalSort => 'Sort';

  @override
  String get plateModalTotal => 'Total';

  @override
  String get plateModalFiltered => 'Filtered';

  @override
  String get plateModalHasHistory => 'Has History';

  @override
  String get plateModalNoHistoryFirst => 'No History First';

  @override
  String get plateModalAlphabetical => 'Alphabetical';

  @override
  String get plateModalSelectPlates => 'Select Plates';

  @override
  String get plateModalQueryAll => 'Query Top 100';

  @override
  String get plateModalPreviousPage => 'Previous Page';

  @override
  String get plateModalNextPage => 'Next Page';

  // Progress bar
  @override
  String get progressLabel => 'Progress';

  @override
  String get availableLabel => 'Available';

  @override
  String get terminateButton => 'Terminate';

  // History screen
  @override
  String get historyTitle => 'History';

  @override
  String get historyEmpty => 'No history records';

  @override
  String get historyEmptyFiltered => 'No records match current filters';

  @override
  String get historySort => 'Sort';

  @override
  String get historyFilter => 'Filter';

  @override
  String get historyClear => 'Clear';

  @override
  String get historyStats => 'Statistics';

  @override
  String get historyAll => 'All';

  @override
  String get historyDateDesc => 'Latest Query';

  @override
  String get historyDateAsc => 'Earliest Query';

  @override
  String get historyPlateNumber => 'Plate Number';

  @override
  String get historyAvailableFirst => 'Available First';

  @override
  String get historyUnavailableFirst => 'Unavailable First';

  @override
  String get historyTotal => 'Total';

  // History context menu
  @override
  String get plateDetailsTitle => 'Plate Details';

  @override
  String get plateNumberLabel => 'Plate Number';

  @override
  String get plateTypeLabel => 'Plate Type';

  @override
  String get vehicleTypeContextLabel => 'Vehicle Type';

  @override
  String get availabilityLabel => 'Availability';

  @override
  String get lastQueryTimeLabel => 'Last Query Time';

  @override
  String get favoriteStatusLabel => 'Favorite Status';

  @override
  String get favoriteStatusFavorited => 'Favorited';

  @override
  String get favoriteStatusNotFavorited => 'Not Favorited';

  @override
  String get closeButton => 'Close';

  @override
  String get retryButton => 'Retry';

  @override
  String get availableStatus => 'Available';

  @override
  String get unavailableStatus => 'Unavailable';

  @override
  String get styleUnavailableMessage => 'Plate available, but style may be unavailable, please call 0294331600';

  // Language switching
  @override
  String get languageSwitchTooltip => 'Select Language';

  @override
  String get systemLanguage => 'System Default';

  @override
  String get chineseLanguage => 'Chinese';

  @override
  String get englishLanguage => 'English';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'Query Complete';

  @override
  String get queryFailedMessage => 'Query Failed';

  // Favorites
  @override
  String get favoritesTitle => 'Favorites';

  @override
  String get favoritesTooltip => 'Favorites';

  @override
  String get favoriteAdd => 'Add to Favorites';

  @override
  String get favoriteRemove => 'Remove from Favorites';

  @override
  String get favoriteAddedMessage => 'Added to favorites';

  @override
  String get favoriteRemovedMessage => 'Removed from favorites';

  @override
  String get favoritesEmpty => 'No favorite records';

  // Help screen
  @override
  String get helpTooltip => 'Help';

  @override
  String get helpTitle => 'Help Documentation';

  @override
  String get helpIntroTitle => 'App Introduction';

  @override
  String get helpIntroContent => 'MyPlate VIC is an application specifically designed to check the availability of New South Wales license plate numbers. You can generate personalized plate combinations based on your preferences and query their availability status in real-time.';

  @override
  String get helpFeaturesTitle => 'Features';

  @override
  String get helpKeywordTitle => 'Keyword Input';

  @override
  String get helpKeywordContent => 'Enter the letters or numbers you want to include in your license plate. Keyword length is limited by vehicle type: light vehicles up to 6 characters, motorcycles up to 5 characters.';

  @override
  String get helpModeTitle => 'Mode Selection';

  @override
  String get helpModeContent => 'Choose where the keyword appears in the plate: prefix mode (keyword at the beginning), suffix mode (keyword at the end), anywhere mode (keyword can be anywhere).';

  @override
  String get helpVehicleTypeTitle => 'Vehicle Type';

  @override
  String get helpVehicleTypeContent => 'Select your vehicle type: car or motorcycle. Different types have different plate formats and rules.';

  @override
  String get helpDigitsTitle => 'Digits Selection';

  @override
  String get helpDigitsContent => 'Choose the total number of digits for the license plate. The system will generate all possible combinations based on your keyword and selected digits.';

  @override
  String get helpProcessTitle => 'Query Process';

  @override
  String get helpProcessStep1 => '1. Enter keyword and select related parameters';

  @override
  String get helpProcessStep2 => '2. Click "Generate Plate Combinations" to view all possible combinations';

  @override
  String get helpProcessStep3 => '3. Select the plates you want to query from the popup list';

  @override
  String get helpProcessStep4 => '4. Click "Start Query" to begin batch availability checking';

  @override
  String get helpProcessStep5 => '5. After completion, automatically navigate to history page to view results';

  @override
  String get helpHistoryTitle => 'History Records';

  @override
  String get helpHistoryContent => 'All query results are saved in history records. You can view each plate\'s availability status, query time, and use filtering and sorting features. Long press on a plate to view details or re-query.';

  @override
  String get helpFavoritesTitle => 'Favorites Feature';

  @override
  String get helpFavoritesContent => 'You can add interesting plate numbers to your favorites list. Click the heart icon in the top-right corner of history cards to favorite them, or use the favorites option in the details menu. Favorited plates can be viewed and managed in the dedicated favorites page.';

  @override
  String get helpFaqTitle => 'Frequently Asked Questions';

  @override
  String get helpFaqQuestion1 => 'Q: Why do some plates show as unavailable?';

  @override
  String get helpFaqAnswer1 => 'A: The plate may already be registered by someone else, or it may not comply with VIC license plate rules.';

  @override
  String get helpFaqQuestion2 => 'Q: How long does querying take?';

  @override
  String get helpFaqAnswer2 => 'A: Query time depends on the number of selected plates, typically 3 - 5 plates per second. The system supports concurrent queries for improved efficiency.';

  @override
  String get helpFaqQuestion3 => 'Q: How many plates can be queried simultaneously?';

  @override
  String get helpFaqAnswer3 => 'A: To protect server resources, a maximum of 100 plates can be selected for querying at once.';

  @override
  String get helpFaqQuestion4 => 'Q: How long are history records kept?';

  @override
  String get helpFaqAnswer4 => 'A: History records are permanently saved on your device until you manually clear them or uninstall the app.';

  @override
  String get helpContactTitle => 'Contact Us';

  @override
  String get helpContactContent => 'If you encounter any issues or have suggestions while using the app, please contact us through app store reviews or relevant channels. We will continuously improve the app experience.';

  // Common
  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get custom => 'Custom';

  @override
  String get personalised => 'Personalised';

  @override
  String get digits => ' digits';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get close => 'Close';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';
}
