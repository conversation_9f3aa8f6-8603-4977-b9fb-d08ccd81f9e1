import 'app_localizations.dart';

class AppLocalizationsDe extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'Verlauf';

  @override
  String get keywordLabel => 'Stichwort (Optional)';

  @override
  String get keywordHint => 'Stichwort eingeben oder leer lassen für alle Kombinationen';

  @override
  String get modeLabel => 'Modus';

  @override
  String get vehicleTypeLabel => 'Fahrzeugtyp';

  @override
  String get digitsLabel => 'Ziffern auswählen';

  @override
  String get generatePlatesButton => 'Kennzeichen-Kombinationen generieren';

  @override
  String get enterKeywordError => 'Bitte Stichwort eingeben';

  @override
  String get selectDigitsError => 'Bitte Anzahl der Ziffern auswählen';

  @override
  String get selectDigitsHint => 'Bitte Anzahl der Ziffern auswählen';

  @override
  String get searchingText => 'Suche läuft...';

  @override
  String get terminatingText => 'Wird beendet...';

  @override
  String get searchCompleteText => 'Suche abgeschlossen! {} verfügbare Kennzeichen gefunden';

  @override
  String get searchTerminatedText => 'Suche vom Benutzer abgebrochen';

  @override
  String get confirmTerminateTitle => 'Beenden bestätigen';

  @override
  String get confirmTerminateContent => 'Sind Sie sicher, dass Sie die laufende Suche beenden möchten?';

  @override
  String get cancelButton => 'Abbrechen';

  @override
  String get confirmTerminateButton => 'Beenden';

  // Keyword modes
  @override
  String get keywordModePrefix => 'Präfix';

  @override
  String get keywordModeSuffix => 'Suffix';

  @override
  String get keywordModeAnywhere => 'Überall';

  @override
  String get keywordModePrefixDesc => 'Stichwort erscheint am Anfang';

  @override
  String get keywordModeSuffixDesc => 'Stichwort erscheint am Ende';

  @override
  String get keywordModeAnywhereDesc => 'Stichwort kann überall erscheinen';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'Auto';

  @override
  String get vehicleTypeMotorcycle => 'Motorrad';

  // Plate modal
  @override
  String get plateModalTitle => 'Kennzeichen-Ergebnisse';

  @override
  String get plateModalLoading => 'Lädt...';

  @override
  String get plateModalError => 'Fehler beim Laden der Kennzeichen';

  @override
  String get plateModalRetry => 'Wiederholen';

  @override
  String get plateModalSelectAll => 'Alle auswählen';

  @override
  String get plateModalDeselectAll => 'Alle abwählen';

  @override
  String get plateModalStartQuery => 'Abfrage starten';

  @override
  String get plateModalClose => 'Schließen';

  @override
  String get plateModalSelected => 'ausgewählt';

  @override
  String get plateModalMaxSelection => 'Maximal {} Kennzeichen können ausgewählt werden';

  @override
  String get plateModalConcurrency => 'Gleichzeitigkeit';

  @override
  String get plateModalCopiedToClipboard => 'In Zwischenablage kopiert';

  @override
  String get plateModalGenerationFailed => 'Kennzeichen-Generierung fehlgeschlagen';

  @override
  String get plateModalNoPlatesWithFilter => 'Keine Kennzeichen mit aktuellen Filtern gefunden';

  @override
  String get plateModalCurrentPage => 'Aktuelle Seite';

  @override
  String get plateModalNoHistory => 'Kein Verlauf';

  @override
  String get plateModalUnavailable => 'Nicht verfügbar';

  @override
  String get plateModalClearSelection => 'Auswahl löschen';

  @override
  String get plateModalFilter => 'Filtern';

  @override
  String get plateModalSort => 'Sortieren';

  @override
  String get plateModalTotal => 'Gesamt';

  @override
  String get plateModalFiltered => 'Gefiltert';

  @override
  String get plateModalHasHistory => 'Mit Verlauf';

  @override
  String get plateModalNoHistoryFirst => 'Ohne Verlauf zuerst';

  @override
  String get plateModalAlphabetical => 'Alphabetisch';

  @override
  String get plateModalSelectPlates => 'Kennzeichen auswählen';

  @override
  String get plateModalQueryAll => 'Top 100 abfragen';

  @override
  String get plateModalPreviousPage => 'Vorherige Seite';

  @override
  String get plateModalNextPage => 'Nächste Seite';

  // Progress bar
  @override
  String get progressLabel => 'Fortschritt';

  @override
  String get availableLabel => 'Verfügbar';

  @override
  String get terminateButton => 'Beenden';

  // History screen
  @override
  String get historyTitle => 'Verlauf';

  @override
  String get historyEmpty => 'Kein Suchverlauf';

  @override
  String get historyEmptyFiltered => 'Keine Ergebnisse mit aktuellen Filtern gefunden';

  @override
  String get historySort => 'Sortieren';

  @override
  String get historyFilter => 'Filtern';

  @override
  String get historyClear => 'Löschen';

  @override
  String get historyStats => 'Statistiken';

  @override
  String get historyAll => 'Alle';

  @override
  String get historyDateDesc => 'Datum (neueste zuerst)';

  @override
  String get historyDateAsc => 'Datum (älteste zuerst)';

  @override
  String get historyPlateNumber => 'Kennzeichen-Nummer';

  @override
  String get historyAvailableFirst => 'Verfügbare zuerst';

  @override
  String get historyUnavailableFirst => 'Nicht verfügbare zuerst';

  @override
  String get historyTotal => 'Gesamt';

  // History context menu
  @override
  String get plateDetailsTitle => 'Kennzeichen-Details';

  @override
  String get plateNumberLabel => 'Kennzeichen-Nummer';

  @override
  String get plateTypeLabel => 'Kennzeichen-Typ';

  @override
  String get vehicleTypeContextLabel => 'Fahrzeugtyp';

  @override
  String get availabilityLabel => 'Verfügbarkeit';

  @override
  String get lastQueryTimeLabel => 'Letzte Abfrage';

  @override
  String get favoriteStatusLabel => 'Favoriten-Status';

  @override
  String get favoriteStatusFavorited => 'Favorisiert';

  @override
  String get favoriteStatusNotFavorited => 'Nicht favorisiert';

  @override
  String get closeButton => 'Schließen';

  @override
  String get retryButton => 'Wiederholen';

  @override
  String get availableStatus => 'Verfügbar';

  @override
  String get unavailableStatus => 'Nicht verfügbar';

  @override
  String get styleUnavailableMessage => 'Kennzeichen verfügbar, aber Stil möglicherweise nicht verfügbar, bitte rufen Sie 0294331600 an';

  // Language switching
  @override
  String get languageSwitchTooltip => 'Sprache wechseln';

  @override
  String get systemLanguage => 'System';

  @override
  String get chineseLanguage => 'Chinesisch';

  @override
  String get englishLanguage => 'Englisch';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'Abfrage abgeschlossen';

  @override
  String get queryFailedMessage => 'Abfrage fehlgeschlagen';

  // Favorites
  @override
  String get favoritesTitle => 'Favoriten';

  @override
  String get favoritesTooltip => 'Favoriten';

  @override
  String get favoriteAdd => 'Zu Favoriten hinzufügen';

  @override
  String get favoriteRemove => 'Aus Favoriten entfernen';

  @override
  String get favoriteAddedMessage => 'Zu Favoriten hinzugefügt';

  @override
  String get favoriteRemovedMessage => 'Aus Favoriten entfernt';

  @override
  String get favoritesEmpty => 'Keine Favoriten-Einträge';

  // Help screen
  @override
  String get helpTooltip => 'Hilfe';

  @override
  String get helpTitle => 'Hilfedokumentation';

  @override
  String get helpIntroTitle => 'App-Einführung';

  @override
  String get helpIntroContent => 'MyPlate VIC ist eine Anwendung, die speziell entwickelt wurde, um die Verfügbarkeit von Kennzeichen-Nummern in New South Wales zu überprüfen. Sie können personalisierte Kennzeichen-Kombinationen nach Ihren Vorlieben generieren und deren Verfügbarkeitsstatus in Echtzeit abfragen.';

  @override
  String get helpFeaturesTitle => 'Funktionen';

  @override
  String get helpKeywordTitle => 'Stichwort-Eingabe';

  @override
  String get helpKeywordContent => 'Geben Sie die Buchstaben oder Zahlen ein, die Sie in Ihrem Kennzeichen haben möchten. Die Stichwort-Länge ist durch den Fahrzeugtyp begrenzt: Leichtfahrzeuge bis zu 6 Zeichen, Motorräder bis zu 5 Zeichen.';

  @override
  String get helpModeTitle => 'Modus-Auswahl';

  @override
  String get helpModeContent => 'Wählen Sie, wo das Stichwort im Kennzeichen erscheint: Präfix-Modus (Stichwort am Anfang), Suffix-Modus (Stichwort am Ende), Beliebig-Modus (Stichwort kann überall sein).';

  @override
  String get helpVehicleTypeTitle => 'Fahrzeugtyp';

  @override
  String get helpVehicleTypeContent => 'Wählen Sie Ihren Fahrzeugtyp: Auto oder Motorrad. Verschiedene Typen haben unterschiedliche Kennzeichen-Formate und Regeln.';

  @override
  String get helpDigitsTitle => 'Ziffern-Auswahl';

  @override
  String get helpDigitsContent => 'Wählen Sie die Gesamtanzahl der Ziffern für das Kennzeichen. Das System generiert alle möglichen Kombinationen basierend auf Ihrem Stichwort und den ausgewählten Ziffern.';

  @override
  String get helpProcessTitle => 'Abfrage-Prozess';

  @override
  String get helpProcessStep1 => '1. Stichwort eingeben und zugehörige Parameter auswählen';

  @override
  String get helpProcessStep2 => '2. "Kennzeichen-Kombinationen generieren" klicken, um alle möglichen Kombinationen zu sehen';

  @override
  String get helpProcessStep3 => '3. Die Kennzeichen auswählen, die Sie abfragen möchten, aus der Popup-Liste';

  @override
  String get helpProcessStep4 => '4. "Abfrage starten" klicken, um die Batch-Verfügbarkeitsprüfung zu beginnen';

  @override
  String get helpProcessStep5 => '5. Nach Abschluss automatisch zur Verlaufsseite navigieren, um Ergebnisse zu sehen';

  @override
  String get helpHistoryTitle => 'Verlaufsdatensätze';

  @override
  String get helpHistoryContent => 'Alle Abfrageergebnisse werden in Verlaufsdatensätzen gespeichert. Sie können den Verfügbarkeitsstatus jedes Kennzeichens, die Abfragezeit sehen und Filter- und Sortierfunktionen verwenden. Lange auf ein Kennzeichen drücken, um Details zu sehen oder erneut abzufragen.';

  @override
  String get helpFavoritesTitle => 'Favoriten-Funktion';

  @override
  String get helpFavoritesContent => 'Sie können interessante Kennzeichennummern zu Ihrer Favoritenliste hinzufügen. Klicken Sie auf das Herz-Symbol in der oberen rechten Ecke der Verlaufskarten, um sie zu favorisieren, oder verwenden Sie die Favoriten-Option im Detailmenü. Favorisierte Kennzeichen können auf der dedizierten Favoritenseite angezeigt und verwaltet werden.';

  @override
  String get helpFaqTitle => 'Häufig gestellte Fragen';

  @override
  String get helpFaqQuestion1 => 'F: Warum zeigen einige Kennzeichen als nicht verfügbar an?';

  @override
  String get helpFaqAnswer1 => 'A: Das Kennzeichen könnte bereits von jemand anderem registriert sein, oder es entspricht möglicherweise nicht den VIC-Kennzeichen-Regeln.';

  @override
  String get helpFaqQuestion2 => 'F: Wie lange dauert die Abfrage?';

  @override
  String get helpFaqAnswer2 => 'A: Die Abfragezeit hängt von der Anzahl der ausgewählten Kennzeichen ab, typischerweise 3-5 Kennzeichen pro Sekunde. Das System unterstützt gleichzeitige Abfragen für verbesserte Effizienz.';

  @override
  String get helpFaqQuestion3 => 'F: Wie viele Kennzeichen können gleichzeitig abgefragt werden?';

  @override
  String get helpFaqAnswer3 => 'A: Um Server-Ressourcen zu schützen, können maximal 100 Kennzeichen gleichzeitig zur Abfrage ausgewählt werden.';

  @override
  String get helpFaqQuestion4 => 'F: Wie lange werden Verlaufsdatensätze aufbewahrt?';

  @override
  String get helpFaqAnswer4 => 'A: Verlaufsdatensätze werden dauerhaft auf Ihrem Gerät gespeichert, bis Sie sie manuell löschen oder die App deinstallieren.';

  @override
  String get helpContactTitle => 'Kontaktieren Sie uns';

  @override
  String get helpContactContent => 'Wenn Sie Probleme haben oder Vorschläge bei der Nutzung der App haben, kontaktieren Sie uns bitte über App Store-Bewertungen oder relevante Kanäle. Wir werden die App-Erfahrung kontinuierlich verbessern.';

  // Common
  @override
  String get available => 'Verfügbar';

  @override
  String get unavailable => 'Nicht verfügbar';

  @override
  String get custom => 'Benutzerdefiniert';

  @override
  String get personalised => 'Personalisiert';

  @override
  String get digits => 'Ziffern';

  @override
  String get loading => 'Lädt';

  @override
  String get error => 'Fehler';

  @override
  String get retry => 'Wiederholen';

  @override
  String get close => 'Schließen';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get confirm => 'Bestätigen';
}
