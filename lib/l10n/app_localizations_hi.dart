import 'app_localizations.dart';

class AppLocalizationsHi extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'इतिहास';

  @override
  String get keywordLabel => 'कीवर्ड (वैकल्पिक)';

  @override
  String get keywordHint => 'कीवर्ड दर्ज करें या सभी संयोजनों के लिए खाली छोड़ें';

  @override
  String get modeLabel => 'मोड';

  @override
  String get vehicleTypeLabel => 'वाहन प्रकार';

  @override
  String get digitsLabel => 'अंक चुनें';

  @override
  String get generatePlatesButton => 'प्लेट संयोजन बनाएं';

  @override
  String get enterKeywordError => 'कृपया कीवर्ड दर्ज करें';

  @override
  String get selectDigitsError => 'कृपया अंकों की संख्या चुनें';

  @override
  String get selectDigitsHint => 'कृपया अंकों की संख्या चुनें';

  @override
  String get searchingText => 'खोज रहे हैं...';

  @override
  String get terminatingText => 'समाप्त कर रहे हैं...';

  @override
  String get searchCompleteText => 'खोज पूर्ण! {} उपलब्ध प्लेट मिली';

  @override
  String get searchTerminatedText => 'उपयोगकर्ता द्वारा खोज रद्द की गई';

  @override
  String get confirmTerminateTitle => 'समाप्ति की पुष्टि करें';

  @override
  String get confirmTerminateContent => 'क्या आप वाकई चल रही खोज को समाप्त करना चाहते हैं?';

  @override
  String get cancelButton => 'रद्द करें';

  @override
  String get confirmTerminateButton => 'समाप्त करें';

  // Keyword modes
  @override
  String get keywordModePrefix => 'उपसर्ग';

  @override
  String get keywordModeSuffix => 'प्रत्यय';

  @override
  String get keywordModeAnywhere => 'कहीं भी';

  @override
  String get keywordModePrefixDesc => 'कीवर्ड शुरुआत में दिखाई देता है';

  @override
  String get keywordModeSuffixDesc => 'कीवर्ड अंत में दिखाई देता है';

  @override
  String get keywordModeAnywhereDesc => 'कीवर्ड कहीं भी दिखाई दे सकता है';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'कार';

  @override
  String get vehicleTypeMotorcycle => 'मोटरसाइकिल';

  // Plate modal
  @override
  String get plateModalTitle => 'प्लेट परिणाम';

  @override
  String get plateModalLoading => 'लोड हो रहा है...';

  @override
  String get plateModalError => 'प्लेट लोड करने में त्रुटि';

  @override
  String get plateModalRetry => 'पुनः प्रयास करें';

  @override
  String get plateModalSelectAll => 'सभी चुनें';

  @override
  String get plateModalDeselectAll => 'सभी अचयनित करें';

  @override
  String get plateModalStartQuery => 'क्वेरी शुरू करें';

  @override
  String get plateModalClose => 'बंद करें';

  @override
  String get plateModalSelected => 'चयनित';

  @override
  String get plateModalMaxSelection => 'अधिकतम {} प्लेट चुनी जा सकती हैं';

  @override
  String get plateModalConcurrency => 'समानांतरता';

  @override
  String get plateModalCopiedToClipboard => 'क्लिपबोर्ड में कॉपी किया गया';

  @override
  String get plateModalGenerationFailed => 'प्लेट जेनरेशन असफल';

  @override
  String get plateModalNoPlatesWithFilter => 'वर्तमान फिल्टर के साथ कोई प्लेट नहीं मिली';

  @override
  String get plateModalCurrentPage => 'वर्तमान पृष्ठ';

  @override
  String get plateModalNoHistory => 'कोई इतिहास नहीं';

  @override
  String get plateModalUnavailable => 'अनुपलब्ध';

  @override
  String get plateModalClearSelection => 'चयन साफ़ करें';

  @override
  String get plateModalFilter => 'फिल्टर';

  @override
  String get plateModalSort => 'क्रमबद्ध करें';

  @override
  String get plateModalTotal => 'कुल';

  @override
  String get plateModalFiltered => 'फिल्टर किया गया';

  @override
  String get plateModalHasHistory => 'इतिहास के साथ';

  @override
  String get plateModalNoHistoryFirst => 'बिना इतिहास पहले';

  @override
  String get plateModalAlphabetical => 'वर्णानुक्रम';

  @override
  String get plateModalSelectPlates => 'प्लेट चुनें';

  @override
  String get plateModalQueryAll => 'टॉप 100 की क्वेरी करें';

  @override
  String get plateModalPreviousPage => 'पिछला पृष्ठ';

  @override
  String get plateModalNextPage => 'अगला पृष्ठ';

  // Progress bar
  @override
  String get progressLabel => 'प्रगति';

  @override
  String get availableLabel => 'उपलब्ध';

  @override
  String get terminateButton => 'समाप्त करें';

  // History screen
  @override
  String get historyTitle => 'इतिहास';

  @override
  String get historyEmpty => 'कोई खोज इतिहास नहीं';

  @override
  String get historyEmptyFiltered => 'वर्तमान फिल्टर के साथ कोई परिणाम नहीं मिला';

  @override
  String get historySort => 'क्रमबद्ध करें';

  @override
  String get historyFilter => 'फिल्टर';

  @override
  String get historyClear => 'साफ़ करें';

  @override
  String get historyStats => 'आंकड़े';

  @override
  String get historyAll => 'सभी';

  @override
  String get historyDateDesc => 'दिनांक (नवीनतम पहले)';

  @override
  String get historyDateAsc => 'दिनांक (पुराने पहले)';

  @override
  String get historyPlateNumber => 'प्लेट नंबर';

  @override
  String get historyAvailableFirst => 'उपलब्ध पहले';

  @override
  String get historyUnavailableFirst => 'अनुपलब्ध पहले';

  @override
  String get historyTotal => 'कुल';

  // History context menu
  @override
  String get plateDetailsTitle => 'प्लेट विवरण';

  @override
  String get plateNumberLabel => 'प्लेट नंबर';

  @override
  String get plateTypeLabel => 'प्लेट प्रकार';

  @override
  String get vehicleTypeContextLabel => 'वाहन प्रकार';

  @override
  String get availabilityLabel => 'उपलब्धता';

  @override
  String get lastQueryTimeLabel => 'अंतिम क्वेरी';

  @override
  String get favoriteStatusLabel => 'पसंदीदा स्थिति';

  @override
  String get favoriteStatusFavorited => 'पसंदीदा';

  @override
  String get favoriteStatusNotFavorited => 'पसंदीदा नहीं';

  @override
  String get closeButton => 'बंद करें';

  @override
  String get retryButton => 'पुनः प्रयास करें';

  @override
  String get availableStatus => 'उपलब्ध';

  @override
  String get unavailableStatus => 'अनुपलब्ध';

  @override
  String get styleUnavailableMessage => 'प्लेट उपलब्ध है, लेकिन शैली अनुपलब्ध हो सकती है, कृपया 0294331600 पर कॉल करें';

  // Language switching
  @override
  String get languageSwitchTooltip => 'भाषा बदलें';

  @override
  String get systemLanguage => 'सिस्टम';

  @override
  String get chineseLanguage => 'चीनी';

  @override
  String get englishLanguage => 'अंग्रेजी';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'क्वेरी पूर्ण';

  @override
  String get queryFailedMessage => 'क्वेरी असफल';

  // Favorites
  @override
  String get favoritesTitle => 'पसंदीदा';

  @override
  String get favoritesTooltip => 'पसंदीदा';

  @override
  String get favoriteAdd => 'पसंदीदा में जोड़ें';

  @override
  String get favoriteRemove => 'पसंदीदा से हटाएं';

  @override
  String get favoriteAddedMessage => 'पसंदीदा में जोड़ा गया';

  @override
  String get favoriteRemovedMessage => 'पसंदीदा से हटाया गया';

  @override
  String get favoritesEmpty => 'कोई पसंदीदा रिकॉर्ड नहीं';

  // Help screen
  @override
  String get helpTooltip => 'सहायता';

  @override
  String get helpTitle => 'सहायता दस्तावेज';

  @override
  String get helpIntroTitle => 'ऐप परिचय';

  @override
  String get helpIntroContent => 'MyPlate VIC न्यू साउथ वेल्स लाइसेंस प्लेट नंबरों की उपलब्धता जांचने के लिए विशेष रूप से डिज़ाइन किया गया एक एप्लिकेशन है।';

  @override
  String get helpFeaturesTitle => 'विशेषताएं';

  @override
  String get helpKeywordTitle => 'कीवर्ड इनपुट';

  @override
  String get helpKeywordContent => 'अपनी लाइसेंस प्लेट में शामिल करने वाले अक्षर या संख्याएं दर्ज करें।';

  @override
  String get helpModeTitle => 'मोड चयन';

  @override
  String get helpModeContent => 'चुनें कि कीवर्ड प्लेट में कहां दिखाई देता है: प्रीफिक्स, सफिक्स, या कहीं भी।';

  @override
  String get helpVehicleTypeTitle => 'वाहन प्रकार';

  @override
  String get helpVehicleTypeContent => 'अपना वाहन प्रकार चुनें: कार या मोटरसाइकिल। विभिन्न प्रकारों के अलग-अलग प्लेट फॉर्मेट और नियम होते हैं।';

  @override
  String get helpDigitsTitle => 'अंक चयन';

  @override
  String get helpDigitsContent => 'लाइसेंस प्लेट के लिए कुल अंकों की संख्या चुनें।';

  @override
  String get helpProcessTitle => 'क्वेरी प्रक्रिया';

  @override
  String get helpProcessStep1 => '1. कीवर्ड दर्ज करें और संबंधित पैरामीटर चुनें';

  @override
  String get helpProcessStep2 => '2. सभी संभावित संयोजन देखने के लिए "प्लेट संयोजन बनाएं" पर क्लिक करें';

  @override
  String get helpProcessStep3 => '3. पॉपअप सूची से उन प्लेटों का चयन करें जिन्हें आप क्वेरी करना चाहते हैं';

  @override
  String get helpProcessStep4 => '4. बैच उपलब्धता जांच शुरू करने के लिए "क्वेरी शुरू करें" पर क्लिक करें';

  @override
  String get helpProcessStep5 => '5. पूरा होने के बाद, परिणाम देखने के लिए स्वचालित रूप से इतिहास पृष्ठ पर नेविगेट करें';

  @override
  String get helpHistoryTitle => 'इतिहास रिकॉर्ड';

  @override
  String get helpHistoryContent => 'सभी क्वेरी परिणाम इतिहास रिकॉर्ड में सहेजे जाते हैं। आप प्रत्येक प्लेट की उपलब्धता स्थिति देख सकते हैं।';

  @override
  String get helpFavoritesTitle => 'पसंदीदा सुविधा';

  @override
  String get helpFavoritesContent => 'आप दिलचस्प प्लेट नंबरों को अपनी पसंदीदा सूची में जोड़ सकते हैं। इतिहास कार्ड के ऊपरी दाएं कोने में हृदय आइकन पर क्लिक करके उन्हें पसंदीदा बनाएं, या विवरण मेनू में पसंदीदा विकल्प का उपयोग करें। पसंदीदा प्लेटों को समर्पित पसंदीदा पृष्ठ में देखा और प्रबंधित किया जा सकता है।';

  @override
  String get helpFaqTitle => 'अक्सर पूछे जाने वाले प्रश्न';

  @override
  String get helpFaqQuestion1 => 'प्र: कुछ प्लेटें अनुपलब्ध क्यों दिखाई देती हैं?';

  @override
  String get helpFaqAnswer1 => 'उ: प्लेट पहले से ही किसी और द्वारा पंजीकृत हो सकती है।';

  @override
  String get helpFaqQuestion2 => 'प्र: क्वेरी में कितना समय लगता है?';

  @override
  String get helpFaqAnswer2 => 'उ: क्वेरी समय चयनित प्लेटों की संख्या पर निर्भर करता है।';

  @override
  String get helpFaqQuestion3 => 'प्र: एक साथ कितनी प्लेटों की क्वेरी की जा सकती है?';

  @override
  String get helpFaqAnswer3 => 'उ: अधिकतम 100 प्लेटों का एक साथ चयन किया जा सकता है।';

  @override
  String get helpFaqQuestion4 => 'प्र: इतिहास रिकॉर्ड कितने समय तक रखे जाते हैं?';

  @override
  String get helpFaqAnswer4 => 'उ: इतिहास रिकॉर्ड आपके डिवाइस पर स्थायी रूप से सहेजे जाते हैं।';

  @override
  String get helpContactTitle => 'हमसे संपर्क करें';

  @override
  String get helpContactContent => 'यदि आपको कोई समस्या आती है या सुझाव हैं, तो कृपया ऐप स्टोर समीक्षाओं के माध्यम से हमसे संपर्क करें।';

  // Common
  @override
  String get available => 'उपलब्ध';

  @override
  String get unavailable => 'अनुपलब्ध';

  @override
  String get custom => 'कस्टम';

  @override
  String get personalised => 'व्यक्तिगत';

  @override
  String get digits => 'अंक';

  @override
  String get loading => 'लोड हो रहा है';

  @override
  String get error => 'त्रुटि';

  @override
  String get retry => 'पुनः प्रयास करें';

  @override
  String get close => 'बंद करें';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get confirm => 'पुष्टि करें';
}
