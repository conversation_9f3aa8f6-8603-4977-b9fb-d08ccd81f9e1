import 'app_localizations.dart';

class AppLocalizationsIt extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'Cronologia';

  @override
  String get keywordLabel => 'Parola chiave (Opzionale)';

  @override
  String get keywordHint => 'Inserisci parola chiave o lascia vuoto per tutte le combinazioni';

  @override
  String get modeLabel => 'Modalità';

  @override
  String get vehicleTypeLabel => 'Tipo di veicolo';

  @override
  String get digitsLabel => 'Seleziona cifre';

  @override
  String get generatePlatesButton => 'Genera combinazioni di targhe';

  @override
  String get enterKeywordError => 'Per favore inserisci una parola chiave';

  @override
  String get selectDigitsError => 'Per favore seleziona il numero di cifre';

  @override
  String get selectDigitsHint => 'Per favore seleziona il numero di cifre';

  @override
  String get searchingText => 'Ricerca in corso...';

  @override
  String get terminatingText => 'Terminando...';

  @override
  String get searchCompleteText => 'Ricerca completata! {} targhe disponibili trovate';

  @override
  String get searchTerminatedText => 'Ricerca annullata dall\'utente';

  @override
  String get confirmTerminateTitle => 'Conferma terminazione';

  @override
  String get confirmTerminateContent => 'Sei sicuro di voler terminare la ricerca in corso?';

  @override
  String get cancelButton => 'Annulla';

  @override
  String get confirmTerminateButton => 'Termina';

  // Keyword modes
  @override
  String get keywordModePrefix => 'Prefisso';

  @override
  String get keywordModeSuffix => 'Suffisso';

  @override
  String get keywordModeAnywhere => 'Ovunque';

  @override
  String get keywordModePrefixDesc => 'La parola chiave appare all\'inizio';

  @override
  String get keywordModeSuffixDesc => 'La parola chiave appare alla fine';

  @override
  String get keywordModeAnywhereDesc => 'La parola chiave può apparire ovunque';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'Auto';

  @override
  String get vehicleTypeMotorcycle => 'Motocicletta';

  // Plate modal
  @override
  String get plateModalTitle => 'Risultati targhe';

  @override
  String get plateModalLoading => 'Caricamento...';

  @override
  String get plateModalError => 'Errore nel caricamento delle targhe';

  @override
  String get plateModalRetry => 'Riprova';

  @override
  String get plateModalSelectAll => 'Seleziona tutto';

  @override
  String get plateModalDeselectAll => 'Deseleziona tutto';

  @override
  String get plateModalStartQuery => 'Avvia query';

  @override
  String get plateModalClose => 'Chiudi';

  @override
  String get plateModalSelected => 'selezionato/i';

  @override
  String get plateModalMaxSelection => 'Possono essere selezionate massimo {} targhe';

  @override
  String get plateModalConcurrency => 'Concorrenza';

  @override
  String get plateModalCopiedToClipboard => 'Copiato negli appunti';

  @override
  String get plateModalGenerationFailed => 'Generazione targhe fallita';

  @override
  String get plateModalNoPlatesWithFilter => 'Nessuna targa trovata con i filtri attuali';

  @override
  String get plateModalCurrentPage => 'Pagina corrente';

  @override
  String get plateModalNoHistory => 'Nessuna cronologia';

  @override
  String get plateModalUnavailable => 'Non disponibile';

  @override
  String get plateModalClearSelection => 'Cancella selezione';

  @override
  String get plateModalFilter => 'Filtra';

  @override
  String get plateModalSort => 'Ordina';

  @override
  String get plateModalTotal => 'Totale';

  @override
  String get plateModalFiltered => 'Filtrato';

  @override
  String get plateModalHasHistory => 'Con cronologia';

  @override
  String get plateModalNoHistoryFirst => 'Senza cronologia prima';

  @override
  String get plateModalAlphabetical => 'Alfabetico';

  @override
  String get plateModalSelectPlates => 'Seleziona targhe';

  @override
  String get plateModalQueryAll => 'Query Top 100';

  @override
  String get plateModalPreviousPage => 'Pagina precedente';

  @override
  String get plateModalNextPage => 'Pagina successiva';

  // Progress bar
  @override
  String get progressLabel => 'Progresso';

  @override
  String get availableLabel => 'Disponibile';

  @override
  String get terminateButton => 'Termina';

  // History screen
  @override
  String get historyTitle => 'Cronologia';

  @override
  String get historyEmpty => 'Nessuna cronologia di ricerca';

  @override
  String get historyEmptyFiltered => 'Nessun risultato trovato con i filtri attuali';

  @override
  String get historySort => 'Ordina';

  @override
  String get historyFilter => 'Filtra';

  @override
  String get historyClear => 'Cancella';

  @override
  String get historyStats => 'Statistiche';

  @override
  String get historyAll => 'Tutto';

  @override
  String get historyDateDesc => 'Data (più recente prima)';

  @override
  String get historyDateAsc => 'Data (più vecchio prima)';

  @override
  String get historyPlateNumber => 'Numero targa';

  @override
  String get historyAvailableFirst => 'Disponibili prima';

  @override
  String get historyUnavailableFirst => 'Non disponibili prima';

  @override
  String get historyTotal => 'Totale';

  // History context menu
  @override
  String get plateDetailsTitle => 'Dettagli targa';

  @override
  String get plateNumberLabel => 'Numero targa';

  @override
  String get plateTypeLabel => 'Tipo targa';

  @override
  String get vehicleTypeContextLabel => 'Tipo veicolo';

  @override
  String get availabilityLabel => 'Disponibilità';

  @override
  String get lastQueryTimeLabel => 'Ultima query';

  @override
  String get closeButton => 'Chiudi';

  @override
  String get retryButton => 'Riprova';

  @override
  String get availableStatus => 'Disponibile';

  @override
  String get unavailableStatus => 'Non disponibile';

  @override
  String get styleUnavailableMessage => 'Targa disponibile, ma lo stile potrebbe non essere disponibile, chiamare il 0294331600';

  // Language switching
  @override
  String get languageSwitchTooltip => 'Cambia lingua';

  @override
  String get systemLanguage => 'Sistema';

  @override
  String get chineseLanguage => 'Cinese';

  @override
  String get englishLanguage => 'Inglese';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'Query completata';

  @override
  String get queryFailedMessage => 'Query fallita';

  // Favorites
  @override
  String get favoritesTitle => 'Preferiti';

  @override
  String get favoritesTooltip => 'Preferiti';

  @override
  String get favoriteAdd => 'Aggiungi ai preferiti';

  @override
  String get favoriteRemove => 'Rimuovi dai preferiti';

  @override
  String get favoriteAddedMessage => 'Aggiunto ai preferiti';

  @override
  String get favoriteRemovedMessage => 'Rimosso dai preferiti';

  @override
  String get favoritesEmpty => 'Nessun record preferito';

  // Favorite status
  @override
  String get favoriteStatusLabel => 'Stato preferiti';

  @override
  String get favoriteStatusFavorited => 'Preferito';

  @override
  String get favoriteStatusNotFavorited => 'Non preferito';

  // Help screen
  @override
  String get helpTooltip => 'Aiuto';

  @override
  String get helpTitle => 'Documentazione di aiuto';

  @override
  String get helpIntroTitle => 'Introduzione dell\'app';

  @override
  String get helpIntroContent => 'MyPlate VIC è un\'applicazione progettata specificamente per verificare la disponibilità dei numeri di targa del Nuovo Galles del Sud.';

  @override
  String get helpFeaturesTitle => 'Caratteristiche';

  @override
  String get helpKeywordTitle => 'Inserimento parola chiave';

  @override
  String get helpKeywordContent => 'Inserisci le lettere o i numeri che vuoi includere nella tua targa.';

  @override
  String get helpModeTitle => 'Selezione modalità';

  @override
  String get helpModeContent => 'Scegli dove appare la parola chiave nella targa: prefisso, suffisso o ovunque.';

  @override
  String get helpVehicleTypeTitle => 'Tipo di veicolo';

  @override
  String get helpVehicleTypeContent => 'Seleziona il tuo tipo di veicolo: auto o motocicletta. I diversi tipi hanno formati e regole di targa differenti.';

  @override
  String get helpDigitsTitle => 'Selezione cifre';

  @override
  String get helpDigitsContent => 'Scegli il numero totale di cifre per la targa.';

  @override
  String get helpProcessTitle => 'Processo di query';

  @override
  String get helpProcessStep1 => '1. Inserisci la parola chiave e seleziona i parametri correlati';

  @override
  String get helpProcessStep2 => '2. Fai clic su "Genera combinazioni di targhe" per vedere tutte le combinazioni possibili';

  @override
  String get helpProcessStep3 => '3. Seleziona le targhe che vuoi interrogare dall\'elenco popup';

  @override
  String get helpProcessStep4 => '4. Fai clic su "Inizia query" per iniziare il controllo di disponibilità in batch';

  @override
  String get helpProcessStep5 => '5. Dopo il completamento, naviga automaticamente alla pagina cronologia per vedere i risultati';

  @override
  String get helpHistoryTitle => 'Record cronologia';

  @override
  String get helpHistoryContent => 'Tutti i risultati delle query sono salvati nei record della cronologia. Puoi vedere lo stato di disponibilità di ogni targa.';

  @override
  String get helpFavoritesTitle => 'Funzione Preferiti';

  @override
  String get helpFavoritesContent => 'Puoi aggiungere numeri di targa interessanti alla tua lista dei preferiti. Clicca sull\'icona del cuore nell\'angolo superiore destro delle schede cronologia per aggiungerle ai preferiti, o usa l\'opzione preferiti nel menu dettagli. Le targhe preferite possono essere visualizzate e gestite nella pagina dedicata ai preferiti.';

  @override
  String get helpFaqTitle => 'Domande frequenti';

  @override
  String get helpFaqQuestion1 => 'D: Perché alcune targhe appaiono come non disponibili?';

  @override
  String get helpFaqAnswer1 => 'R: La targa potrebbe essere già registrata da qualcun altro.';

  @override
  String get helpFaqQuestion2 => 'D: Quanto tempo richiede la query?';

  @override
  String get helpFaqAnswer2 => 'R: Il tempo di query dipende dal numero di targhe selezionate.';

  @override
  String get helpFaqQuestion3 => 'D: Quante targhe possono essere interrogate simultaneamente?';

  @override
  String get helpFaqAnswer3 => 'R: Un massimo di 100 targhe può essere selezionato per la query alla volta.';

  @override
  String get helpFaqQuestion4 => 'D: Per quanto tempo vengono mantenuti i record della cronologia?';

  @override
  String get helpFaqAnswer4 => 'R: I record della cronologia sono salvati permanentemente sul tuo dispositivo.';

  @override
  String get helpContactTitle => 'Contattaci';

  @override
  String get helpContactContent => 'Se hai problemi o suggerimenti, contattaci tramite le recensioni dell\'app store.';

  // Common
  @override
  String get available => 'Disponibile';

  @override
  String get unavailable => 'Non disponibile';

  @override
  String get custom => 'Personalizzato';

  @override
  String get personalised => 'Personalizzato';

  @override
  String get digits => 'cifre';

  @override
  String get loading => 'Caricamento';

  @override
  String get error => 'Errore';

  @override
  String get retry => 'Riprova';

  @override
  String get close => 'Chiudi';

  @override
  String get cancel => 'Annulla';

  @override
  String get confirm => 'Conferma';
}
