import 'app_localizations.dart';

class AppLocalizationsZh extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => '历史记录';

  @override
  String get keywordLabel => '关键词 (可选)';

  @override
  String get keywordHint => '输入关键词或留空生成所有组合';

  @override
  String get modeLabel => '模式';

  @override
  String get vehicleTypeLabel => '车辆类型';

  @override
  String get digitsLabel => '选择位数';

  @override
  String get generatePlatesButton => '生成车牌组合';

  @override
  String get enterKeywordError => '请输入关键词';

  @override
  String get selectDigitsError => '请选择号码长度';

  @override
  String get selectDigitsHint => '请选择号码长度';

  @override
  String get searchingText => '正在查询中...';

  @override
  String get terminatingText => '正在终止查询...';

  @override
  String get searchCompleteText => '查询完成！找到 {} 个可用车牌';

  @override
  String get searchTerminatedText => '查询已终止！已找到 {} 个可用车牌';

  @override
  String get confirmTerminateTitle => '确认终止';

  @override
  String get confirmTerminateContent => '确定要终止当前查询吗？已查询的结果将会保留。';

  @override
  String get cancelButton => '取消';

  @override
  String get confirmTerminateButton => '确定终止';

  // Keyword modes
  @override
  String get keywordModePrefix => '前缀模式';

  @override
  String get keywordModeSuffix => '后缀模式';

  @override
  String get keywordModeAnywhere => '任意模式';

  @override
  String get keywordModePrefixDesc => '关键词出现在开头';

  @override
  String get keywordModeSuffixDesc => '关键词出现在结尾';

  @override
  String get keywordModeAnywhereDesc => '关键词可在任意位置';

  // Vehicle types
  @override
  String get vehicleTypeCar => '汽车';

  @override
  String get vehicleTypeMotorcycle => '摩托车';

  // Plate modal
  @override
  String get plateModalTitle => '生成的车牌组合';

  @override
  String get plateModalLoading => '正在生成车牌组合...';

  @override
  String get plateModalError => '生成车牌组合时出错';

  @override
  String get plateModalRetry => '重试';

  @override
  String get plateModalSelectAll => '全选';

  @override
  String get plateModalDeselectAll => '取消全选';

  @override
  String get plateModalStartQuery => '开始查询';

  @override
  String get plateModalClose => '关闭';

  @override
  String get plateModalSelected => '已选择 {} 个';

  @override
  String get plateModalMaxSelection => '最多只能选择 {} 个车牌';

  @override
  String get plateModalConcurrency => '并发数';

  @override
  String get plateModalCopiedToClipboard => '已复制到剪贴板';

  @override
  String get plateModalGenerationFailed => '生成车牌失败';

  @override
  String get plateModalNoPlatesWithFilter => '当前过滤条件下没有车牌';

  @override
  String get plateModalCurrentPage => '当前页';

  @override
  String get plateModalNoHistory => '无历史';

  @override
  String get plateModalUnavailable => '不可用';

  @override
  String get plateModalClearSelection => '清空选择';

  @override
  String get plateModalFilter => '过滤';

  @override
  String get plateModalSort => '排序';

  @override
  String get plateModalTotal => '总计';

  @override
  String get plateModalFiltered => '过滤后';

  @override
  String get plateModalHasHistory => '有历史';

  @override
  String get plateModalNoHistoryFirst => '优先无历史';

  @override
  String get plateModalAlphabetical => '字母顺序';

  @override
  String get plateModalSelectPlates => '选择车牌';

  @override
  String get plateModalQueryAll => '查询前100个';

  @override
  String get plateModalPreviousPage => '上一页';

  @override
  String get plateModalNextPage => '下一页';

  // Progress bar
  @override
  String get progressLabel => '进度';

  @override
  String get availableLabel => '可用';

  @override
  String get terminateButton => '终止';

  // History screen
  @override
  String get historyTitle => '历史记录';

  @override
  String get historyEmpty => '暂无历史记录';

  @override
  String get historyEmptyFiltered => '当前过滤条件下没有记录';

  @override
  String get historySort => '排序';

  @override
  String get historyFilter => '过滤';

  @override
  String get historyClear => '清空';

  @override
  String get historyStats => '统计';

  @override
  String get historyAll => '全部';

  @override
  String get historyDateDesc => '最新查询';

  @override
  String get historyDateAsc => '最早查询';

  @override
  String get historyPlateNumber => '车牌号码';

  @override
  String get historyAvailableFirst => '可用优先';

  @override
  String get historyUnavailableFirst => '不可用优先';

  @override
  String get historyTotal => '总计';

  // History context menu
  @override
  String get plateDetailsTitle => '车牌详情';

  @override
  String get plateNumberLabel => '车牌号码';

  @override
  String get plateTypeLabel => '车牌类型';

  @override
  String get vehicleTypeContextLabel => '车辆类型';

  @override
  String get availabilityLabel => '可用状态';

  @override
  String get lastQueryTimeLabel => '最近查询时间';

  @override
  String get favoriteStatusLabel => '收藏状态';

  @override
  String get favoriteStatusFavorited => '已收藏';

  @override
  String get favoriteStatusNotFavorited => '未收藏';

  @override
  String get closeButton => '关闭';

  @override
  String get retryButton => '重试';

  @override
  String get availableStatus => '可用';

  @override
  String get unavailableStatus => '不可用';

  @override
  String get styleUnavailableMessage => '号码可用，但样式可能不可用，请致电0294331600';

  // Language switching
  @override
  String get languageSwitchTooltip => '选择语言';

  @override
  String get systemLanguage => '跟随系统';

  @override
  String get chineseLanguage => '中文';

  @override
  String get englishLanguage => '英文';

  // History screen notifications
  @override
  String get queryCompleteMessage => '查询完成';

  @override
  String get queryFailedMessage => '查询失败';

  // Favorites
  @override
  String get favoritesTitle => '收藏列表';

  @override
  String get favoritesTooltip => '收藏列表';

  @override
  String get favoriteAdd => '添加收藏';

  @override
  String get favoriteRemove => '取消收藏';

  @override
  String get favoriteAddedMessage => '已添加到收藏';

  @override
  String get favoriteRemovedMessage => '已从收藏中移除';

  @override
  String get favoritesEmpty => '暂无收藏记录';

  // Help screen
  @override
  String get helpTooltip => '帮助';

  @override
  String get helpTitle => '帮助文档';

  @override
  String get helpIntroTitle => '应用介绍';

  @override
  String get helpIntroContent => 'MyPlates VIC 是一个专门用于查询新南威尔士州车牌号码可用性的应用。您可以根据自己的喜好生成个性化车牌组合，并实时查询这些号码的可用状态。';

  @override
  String get helpFeaturesTitle => '功能说明';

  @override
  String get helpKeywordTitle => '关键词输入';

  @override
  String get helpKeywordContent => '输入您希望包含在车牌中的字母或数字组合。关键词长度根据车辆类型限制：轻型车辆最多6位，摩托车最多5位。';

  @override
  String get helpModeTitle => '模式选择';

  @override
  String get helpModeContent => '选择关键词在车牌中的位置：前缀模式（关键词在开头）、后缀模式（关键词在结尾）、任意模式（关键词可在任意位置）。';

  @override
  String get helpVehicleTypeTitle => '车辆类型';

  @override
  String get helpVehicleTypeContent => '选择您的车辆类型：汽车或摩托车。不同类型有不同的车牌格式和规则。';

  @override
  String get helpDigitsTitle => '位数选择';

  @override
  String get helpDigitsContent => '选择车牌号码的总位数。系统会根据您的关键词和选择的位数生成所有可能的组合。';

  @override
  String get helpProcessTitle => '查询流程';

  @override
  String get helpProcessStep1 => '1. 输入关键词并选择相关参数';

  @override
  String get helpProcessStep2 => '2. 点击"生成车牌组合"查看所有可能的组合';

  @override
  String get helpProcessStep3 => '3. 在弹出的列表中选择您想要查询的车牌';

  @override
  String get helpProcessStep4 => '4. 点击"开始查询"开始批量查询可用性';

  @override
  String get helpProcessStep5 => '5. 查询完成后自动跳转到历史记录页面查看结果';

  @override
  String get helpHistoryTitle => '历史记录';

  @override
  String get helpHistoryContent => '所有查询结果都会保存在历史记录中。您可以查看每个车牌的可用状态、查询时间，并支持过滤和排序功能。长按车牌可查看详细信息或重新查询。';

  @override
  String get helpFavoritesTitle => '收藏功能';

  @override
  String get helpFavoritesContent => '您可以将感兴趣的车牌号码添加到收藏列表中。点击历史记录卡片右上角的心形图标即可收藏，或在详情菜单中进行收藏操作。收藏的车牌可以在专门的收藏页面中查看和管理。';

  @override
  String get helpFaqTitle => '常见问题';

  @override
  String get helpFaqQuestion1 => '问：为什么有些车牌显示不可用？';

  @override
  String get helpFaqAnswer1 => '答：车牌可能已被他人注册，或者不符合NSW的车牌规则要求。';

  @override
  String get helpFaqQuestion2 => '问：查询需要多长时间？';

  @override
  String get helpFaqAnswer2 => '答：查询时间取决于选择的车牌数量，通常每秒可查询3-5个车牌。系统支持并发查询以提高效率。';

  @override
  String get helpFaqQuestion3 => '问：可以同时查询多少个车牌？';

  @override
  String get helpFaqAnswer3 => '答：为了保护服务器资源，单次最多可选择100个车牌进行查询。';

  @override
  String get helpFaqQuestion4 => '问：历史记录会保存多久？';

  @override
  String get helpFaqAnswer4 => '答：历史记录会永久保存在您的设备上，直到您手动清除或卸载应用。';

  @override
  String get helpContactTitle => '联系我们';

  @override
  String get helpContactContent => '如果您在使用过程中遇到问题或有任何建议，请通过应用商店评价或相关渠道联系我们。我们会持续改进应用体验。';

  // Common
  @override
  String get available => '可用';

  @override
  String get unavailable => '不可用';

  @override
  String get custom => 'Custom';

  @override
  String get personalised => 'Personalised';

  @override
  String get digits => '位';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get retry => '重试';

  @override
  String get close => '关闭';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';
}
