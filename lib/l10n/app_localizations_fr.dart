import 'app_localizations.dart';

class AppLocalizationsFr extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'Historique';

  @override
  String get keywordLabel => 'Mot-clé (Optionnel)';

  @override
  String get keywordHint => 'Entrer un mot-clé ou laisser vide pour toutes les combinaisons';

  @override
  String get modeLabel => 'Mode';

  @override
  String get vehicleTypeLabel => 'Type de véhicule';

  @override
  String get digitsLabel => 'Sélectionner les chiffres';

  @override
  String get generatePlatesButton => 'Générer les combinaisons de plaques';

  @override
  String get enterKeywordError => 'Veuillez entrer un mot-clé';

  @override
  String get selectDigitsError => 'Veuillez sélectionner le nombre de chiffres';

  @override
  String get selectDigitsHint => 'Veuillez sélectionner le nombre de chiffres';

  @override
  String get searchingText => 'Recherche en cours...';

  @override
  String get terminatingText => 'Arrêt en cours...';

  @override
  String get searchCompleteText => 'Recherche terminée ! {} plaques disponibles trouvées';

  @override
  String get searchTerminatedText => 'Recherche interrompue par l\'utilisateur';

  @override
  String get confirmTerminateTitle => 'Confirmer l\'arrêt';

  @override
  String get confirmTerminateContent => 'Êtes-vous sûr de vouloir arrêter la recherche en cours ?';

  @override
  String get cancelButton => 'Annuler';

  @override
  String get confirmTerminateButton => 'Arrêter';

  // Keyword modes
  @override
  String get keywordModePrefix => 'Préfixe';

  @override
  String get keywordModeSuffix => 'Suffixe';

  @override
  String get keywordModeAnywhere => 'N\'importe où';

  @override
  String get keywordModePrefixDesc => 'Le mot-clé apparaît au début';

  @override
  String get keywordModeSuffixDesc => 'Le mot-clé apparaît à la fin';

  @override
  String get keywordModeAnywhereDesc => 'Le mot-clé peut apparaître n\'importe où';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'Voiture';

  @override
  String get vehicleTypeMotorcycle => 'Moto';

  // Plate modal
  @override
  String get plateModalTitle => 'Résultats des plaques';

  @override
  String get plateModalLoading => 'Chargement...';

  @override
  String get plateModalError => 'Erreur lors du chargement des plaques';

  @override
  String get plateModalRetry => 'Réessayer';

  @override
  String get plateModalSelectAll => 'Tout sélectionner';

  @override
  String get plateModalDeselectAll => 'Tout désélectionner';

  @override
  String get plateModalStartQuery => 'Démarrer la requête';

  @override
  String get plateModalClose => 'Fermer';

  @override
  String get plateModalSelected => 'sélectionné(s)';

  @override
  String get plateModalMaxSelection => 'Maximum {} plaques peuvent être sélectionnées';

  @override
  String get plateModalConcurrency => 'Concurrence';

  @override
  String get plateModalCopiedToClipboard => 'Copié dans le presse-papiers';

  @override
  String get plateModalGenerationFailed => 'Échec de la génération des plaques';

  @override
  String get plateModalNoPlatesWithFilter => 'Aucune plaque trouvée avec les filtres actuels';

  @override
  String get plateModalCurrentPage => 'Page actuelle';

  @override
  String get plateModalNoHistory => 'Pas d\'historique';

  @override
  String get plateModalUnavailable => 'Indisponible';

  @override
  String get plateModalClearSelection => 'Effacer la sélection';

  @override
  String get plateModalFilter => 'Filtrer';

  @override
  String get plateModalSort => 'Trier';

  @override
  String get plateModalTotal => 'Total';

  @override
  String get plateModalFiltered => 'Filtré';

  @override
  String get plateModalHasHistory => 'Avec historique';

  @override
  String get plateModalNoHistoryFirst => 'Sans historique en premier';

  @override
  String get plateModalAlphabetical => 'Alphabétique';

  @override
  String get plateModalSelectPlates => 'Sélectionner les plaques';

  @override
  String get plateModalQueryAll => 'Requête Top 100';

  @override
  String get plateModalPreviousPage => 'Page précédente';

  @override
  String get plateModalNextPage => 'Page suivante';

  // Progress bar
  @override
  String get progressLabel => 'Progression';

  @override
  String get availableLabel => 'Disponible';

  @override
  String get terminateButton => 'Arrêter';

  // History screen
  @override
  String get historyTitle => 'Historique';

  @override
  String get historyEmpty => 'Aucun historique de recherche';

  @override
  String get historyEmptyFiltered => 'Aucun résultat trouvé avec les filtres actuels';

  @override
  String get historySort => 'Trier';

  @override
  String get historyFilter => 'Filtrer';

  @override
  String get historyClear => 'Effacer';

  @override
  String get historyStats => 'Statistiques';

  @override
  String get historyAll => 'Tout';

  @override
  String get historyDateDesc => 'Date (récent en premier)';

  @override
  String get historyDateAsc => 'Date (ancien en premier)';

  @override
  String get historyPlateNumber => 'Numéro de plaque';

  @override
  String get historyAvailableFirst => 'Disponible en premier';

  @override
  String get historyUnavailableFirst => 'Indisponible en premier';

  @override
  String get historyTotal => 'Total';

  // History context menu
  @override
  String get plateDetailsTitle => 'Détails de la plaque';

  @override
  String get plateNumberLabel => 'Numéro de plaque';

  @override
  String get plateTypeLabel => 'Type de plaque';

  @override
  String get vehicleTypeContextLabel => 'Type de véhicule';

  @override
  String get availabilityLabel => 'Disponibilité';

  @override
  String get lastQueryTimeLabel => 'Dernière requête';

  @override
  String get favoriteStatusLabel => 'Statut des favoris';

  @override
  String get favoriteStatusFavorited => 'Favorisé';

  @override
  String get favoriteStatusNotFavorited => 'Non favorisé';

  @override
  String get closeButton => 'Fermer';

  @override
  String get retryButton => 'Réessayer';

  @override
  String get availableStatus => 'Disponible';

  @override
  String get unavailableStatus => 'Indisponible';

  @override
  String get styleUnavailableMessage => 'Plaque disponible, mais le style peut être indisponible, veuillez appeler le 0294331600';

  // Language switching
  @override
  String get languageSwitchTooltip => 'Changer de langue';

  @override
  String get systemLanguage => 'Système';

  @override
  String get chineseLanguage => 'Chinois';

  @override
  String get englishLanguage => 'Anglais';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'Requête terminée';

  @override
  String get queryFailedMessage => 'Échec de la requête';

  // Favorites
  @override
  String get favoritesTitle => 'Favoris';

  @override
  String get favoritesTooltip => 'Favoris';

  @override
  String get favoriteAdd => 'Ajouter aux favoris';

  @override
  String get favoriteRemove => 'Retirer des favoris';

  @override
  String get favoriteAddedMessage => 'Ajouté aux favoris';

  @override
  String get favoriteRemovedMessage => 'Retiré des favoris';

  @override
  String get favoritesEmpty => 'Aucun enregistrement favori';

  // Help screen
  @override
  String get helpTooltip => 'Aide';

  @override
  String get helpTitle => 'Documentation d\'aide';

  @override
  String get helpIntroTitle => 'Introduction de l\'application';

  @override
  String get helpIntroContent => 'MyPlate VIC est une application spécialement conçue pour vérifier la disponibilité des numéros de plaques d\'immatriculation de Nouvelle-Galles du Sud. Vous pouvez générer des combinaisons de plaques personnalisées selon vos préférences et vérifier leur statut de disponibilité en temps réel.';

  @override
  String get helpFeaturesTitle => 'Fonctionnalités';

  @override
  String get helpKeywordTitle => 'Saisie de mot-clé';

  @override
  String get helpKeywordContent => 'Entrez les lettres ou chiffres que vous souhaitez inclure dans votre plaque d\'immatriculation. La longueur du mot-clé est limitée par le type de véhicule : véhicules légers jusqu\'à 6 caractères, motos jusqu\'à 5 caractères.';

  @override
  String get helpModeTitle => 'Sélection du mode';

  @override
  String get helpModeContent => 'Choisissez où le mot-clé apparaît dans la plaque : mode préfixe (mot-clé au début), mode suffixe (mot-clé à la fin), mode n\'importe où (mot-clé peut être n\'importe où).';

  @override
  String get helpVehicleTypeTitle => 'Type de véhicule';

  @override
  String get helpVehicleTypeContent => 'Sélectionnez votre type de véhicule : voiture ou moto. Différents types ont différents formats et règles de plaques.';

  @override
  String get helpDigitsTitle => 'Sélection des chiffres';

  @override
  String get helpDigitsContent => 'Choisissez le nombre total de chiffres pour la plaque d\'immatriculation. Le système générera toutes les combinaisons possibles basées sur votre mot-clé et les chiffres sélectionnés.';

  @override
  String get helpProcessTitle => 'Processus de requête';

  @override
  String get helpProcessStep1 => '1. Entrez le mot-clé et sélectionnez les paramètres associés';

  @override
  String get helpProcessStep2 => '2. Cliquez sur "Générer les combinaisons de plaques" pour voir toutes les combinaisons possibles';

  @override
  String get helpProcessStep3 => '3. Sélectionnez les plaques que vous voulez interroger dans la liste popup';

  @override
  String get helpProcessStep4 => '4. Cliquez sur "Commencer la requête" pour commencer la vérification de disponibilité par lot';

  @override
  String get helpProcessStep5 => '5. Après achèvement, naviguez automatiquement vers la page d\'historique pour voir les résultats';

  @override
  String get helpHistoryTitle => 'Enregistrements d\'historique';

  @override
  String get helpHistoryContent => 'Tous les résultats de requête sont sauvegardés dans les enregistrements d\'historique. Vous pouvez voir le statut de disponibilité de chaque plaque, l\'heure de requête, et utiliser les fonctionnalités de filtrage et tri. Appuyez longuement sur une plaque pour voir les détails ou relancer la requête.';

  @override
  String get helpFavoritesTitle => 'Fonction Favoris';

  @override
  String get helpFavoritesContent => 'Vous pouvez ajouter des numéros de plaque intéressants à votre liste de favoris. Cliquez sur l\'icône cœur dans le coin supérieur droit des cartes d\'historique pour les mettre en favoris, ou utilisez l\'option favoris dans le menu détails. Les plaques favorites peuvent être consultées et gérées dans la page dédiée aux favoris.';

  @override
  String get helpFaqTitle => 'Questions fréquemment posées';

  @override
  String get helpFaqQuestion1 => 'Q: Pourquoi certaines plaques apparaissent comme indisponibles?';

  @override
  String get helpFaqAnswer1 => 'R: La plaque peut déjà être enregistrée par quelqu\'un d\'autre, ou elle peut ne pas respecter les règles des plaques VIC.';

  @override
  String get helpFaqQuestion2 => 'Q: Combien de temps prend la requête?';

  @override
  String get helpFaqAnswer2 => 'R: Le temps de requête dépend du nombre de plaques sélectionnées, typiquement 3-5 plaques par seconde. Le système supporte les requêtes concurrentes pour une efficacité améliorée.';

  @override
  String get helpFaqQuestion3 => 'Q: Combien de plaques peuvent être interrogées simultanément?';

  @override
  String get helpFaqAnswer3 => 'R: Pour protéger les ressources du serveur, un maximum de 100 plaques peut être sélectionné pour interrogation à la fois.';

  @override
  String get helpFaqQuestion4 => 'Q: Combien de temps les enregistrements d\'historique sont-ils conservés?';

  @override
  String get helpFaqAnswer4 => 'R: Les enregistrements d\'historique sont sauvegardés de façon permanente sur votre appareil jusqu\'à ce que vous les effaciez manuellement ou désinstalliez l\'application.';

  @override
  String get helpContactTitle => 'Nous contacter';

  @override
  String get helpContactContent => 'Si vous rencontrez des problèmes ou avez des suggestions lors de l\'utilisation de l\'application, veuillez nous contacter via les avis de l\'app store ou les canaux pertinents. Nous améliorerons continuellement l\'expérience de l\'application.';

  // Common
  @override
  String get available => 'Disponible';

  @override
  String get unavailable => 'Indisponible';

  @override
  String get custom => 'Personnalisé';

  @override
  String get personalised => 'Personnalisé';

  @override
  String get digits => 'chiffres';

  @override
  String get loading => 'Chargement';

  @override
  String get error => 'Erreur';

  @override
  String get retry => 'Réessayer';

  @override
  String get close => 'Fermer';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';
}
