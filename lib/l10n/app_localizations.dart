import 'package:flutter/material.dart';

abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // App title
  String get appTitle;

  // Home screen
  String get homeTitle;
  String get historyTooltip;
  String get keywordLabel;
  String get keywordHint;
  String get modeLabel;
  String get vehicleTypeLabel;
  String get digitsLabel;
  String get generatePlatesButton;
  String get enterKeywordError;
  String get selectDigitsError;
  String get selectDigitsHint;
  String get searchingText;
  String get terminatingText;
  String get searchCompleteText;
  String get searchTerminatedText;
  String get confirmTerminateTitle;
  String get confirmTerminateContent;
  String get cancelButton;
  String get confirmTerminateButton;

  // Keyword modes
  String get keywordModePrefix;
  String get keywordModeSuffix;
  String get keywordModeAnywhere;
  String get keywordModePrefixDesc;
  String get keywordModeSuffixDesc;
  String get keywordModeAnywhereDesc;

  // Vehicle types
  String get vehicleTypeCar;
  String get vehicleTypeMotorcycle;

  // Plate modal
  String get plateModalTitle;
  String get plateModalLoading;
  String get plateModalError;
  String get plateModalRetry;
  String get plateModalSelectAll;
  String get plateModalDeselectAll;
  String get plateModalStartQuery;
  String get plateModalClose;
  String get plateModalSelected;
  String get plateModalMaxSelection;
  String get plateModalConcurrency;
  String get plateModalCopiedToClipboard;
  String get plateModalGenerationFailed;
  String get plateModalNoPlatesWithFilter;
  String get plateModalCurrentPage;
  String get plateModalNoHistory;
  String get plateModalUnavailable;
  String get plateModalClearSelection;
  String get plateModalFilter;
  String get plateModalSort;
  String get plateModalTotal;
  String get plateModalFiltered;
  String get plateModalHasHistory;
  String get plateModalNoHistoryFirst;
  String get plateModalAlphabetical;
  String get plateModalSelectPlates;
  String get plateModalQueryAll;
  String get plateModalPreviousPage;
  String get plateModalNextPage;

  // Progress bar
  String get progressLabel;
  String get availableLabel;
  String get terminateButton;

  // History screen
  String get historyTitle;
  String get historyEmpty;
  String get historyEmptyFiltered;
  String get historySort;
  String get historyFilter;
  String get historyClear;
  String get historyStats;
  String get historyAll;
  String get historyDateDesc;
  String get historyDateAsc;
  String get historyPlateNumber;
  String get historyAvailableFirst;
  String get historyUnavailableFirst;
  String get historyTotal;

  // History context menu
  String get plateDetailsTitle;
  String get plateNumberLabel;
  String get plateTypeLabel;
  String get vehicleTypeContextLabel;
  String get availabilityLabel;
  String get lastQueryTimeLabel;
  String get favoriteStatusLabel;
  String get favoriteStatusFavorited;
  String get favoriteStatusNotFavorited;
  String get closeButton;
  String get retryButton;
  String get availableStatus;
  String get unavailableStatus;
  String get styleUnavailableMessage;

  // Language switching
  String get languageSwitchTooltip;
  String get systemLanguage;
  String get chineseLanguage;
  String get englishLanguage;

  // History screen notifications
  String get queryCompleteMessage;
  String get queryFailedMessage;

  // Favorites
  String get favoritesTitle;
  String get favoritesTooltip;
  String get favoriteAdd;
  String get favoriteRemove;
  String get favoriteAddedMessage;
  String get favoriteRemovedMessage;
  String get favoritesEmpty;

  // Help screen
  String get helpTooltip;
  String get helpTitle;
  String get helpIntroTitle;
  String get helpIntroContent;
  String get helpFeaturesTitle;
  String get helpKeywordTitle;
  String get helpKeywordContent;
  String get helpModeTitle;
  String get helpModeContent;
  String get helpVehicleTypeTitle;
  String get helpVehicleTypeContent;
  String get helpDigitsTitle;
  String get helpDigitsContent;
  String get helpProcessTitle;
  String get helpProcessStep1;
  String get helpProcessStep2;
  String get helpProcessStep3;
  String get helpProcessStep4;
  String get helpProcessStep5;
  String get helpHistoryTitle;
  String get helpHistoryContent;
  String get helpFavoritesTitle;
  String get helpFavoritesContent;
  String get helpFaqTitle;
  String get helpFaqQuestion1;
  String get helpFaqAnswer1;
  String get helpFaqQuestion2;
  String get helpFaqAnswer2;
  String get helpFaqQuestion3;
  String get helpFaqAnswer3;
  String get helpFaqQuestion4;
  String get helpFaqAnswer4;
  String get helpContactTitle;
  String get helpContactContent;

  // Common
  String get available;
  String get unavailable;
  String get custom;
  String get personalised;
  String get digits;
  String get loading;
  String get error;
  String get retry;
  String get close;
  String get cancel;
  String get confirm;
}
