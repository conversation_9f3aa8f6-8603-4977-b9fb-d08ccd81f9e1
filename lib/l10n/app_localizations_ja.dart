import 'app_localizations.dart';

class AppLocalizationsJa extends AppLocalizations {
  // Vehicle types
  @override
  String get vehicleTypeCar => '自動車';

  @override
  String get vehicleTypeMotorcycle => 'オートバイ';

  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => '履歴';

  @override
  String get keywordLabel => 'キーワード (オプション)';

  @override
  String get keywordHint => 'キーワードを入力するか、空欄にしてすべての組み合わせを生成';

  @override
  String get modeLabel => 'モード';

  @override
  String get vehicleTypeLabel => '車両タイプ';

  @override
  String get digitsLabel => '桁数を選択';

  @override
  String get generatePlatesButton => 'プレート組み合わせを生成';

  @override
  String get enterKeywordError => 'キーワードを入力してください';

  @override
  String get selectDigitsError => '桁数を選択してください';

  @override
  String get selectDigitsHint => '桁数を選択してください';

  @override
  String get searchingText => '検索中...';

  @override
  String get terminatingText => '終了中...';

  @override
  String get searchCompleteText => '検索完了！{}個の利用可能なプレートが見つかりました';

  @override
  String get searchTerminatedText => 'ユーザーによって検索がキャンセルされました';

  @override
  String get confirmTerminateTitle => '終了の確認';

  @override
  String get confirmTerminateContent => '進行中の検索を終了してもよろしいですか？';

  @override
  String get cancelButton => 'キャンセル';

  @override
  String get confirmTerminateButton => '終了';

  // Keyword modes
  @override
  String get keywordModePrefix => 'プレフィックス';

  @override
  String get keywordModeSuffix => 'サフィックス';

  @override
  String get keywordModeAnywhere => 'どこでも';

  @override
  String get keywordModePrefixDesc => 'キーワードが最初に表示される';

  @override
  String get keywordModeSuffixDesc => 'キーワードが最後に表示される';

  @override
  String get keywordModeAnywhereDesc => 'キーワードがどこにでも表示される';

  // Plate modal
  @override
  String get plateModalTitle => 'プレート結果';

  @override
  String get plateModalLoading => '読み込み中...';

  @override
  String get plateModalError => 'プレートの読み込みエラー';

  @override
  String get plateModalRetry => '再試行';

  @override
  String get plateModalSelectAll => 'すべて選択';

  @override
  String get plateModalDeselectAll => 'すべて選択解除';

  @override
  String get plateModalStartQuery => 'クエリ開始';

  @override
  String get plateModalClose => '閉じる';

  @override
  String get plateModalSelected => '選択済み';

  @override
  String get plateModalMaxSelection => '最大{}個のプレートを選択できます';

  @override
  String get plateModalConcurrency => '同時実行';

  @override
  String get plateModalCopiedToClipboard => 'クリップボードにコピーしました';

  @override
  String get plateModalGenerationFailed => 'プレート生成に失敗しました';

  @override
  String get plateModalNoPlatesWithFilter => '現在のフィルターでプレートが見つかりません';

  @override
  String get plateModalCurrentPage => '現在のページ';

  @override
  String get plateModalNoHistory => '履歴なし';

  @override
  String get plateModalUnavailable => '利用不可';

  @override
  String get plateModalClearSelection => '選択をクリア';

  @override
  String get plateModalFilter => 'フィルター';

  @override
  String get plateModalSort => 'ソート';

  @override
  String get plateModalTotal => '合計';

  @override
  String get plateModalFiltered => 'フィルター済み';

  @override
  String get plateModalHasHistory => '履歴あり';

  @override
  String get plateModalNoHistoryFirst => '履歴なしを最初に';

  @override
  String get plateModalAlphabetical => 'アルファベット順';

  @override
  String get plateModalSelectPlates => 'プレートを選択';

  @override
  String get plateModalQueryAll => 'トップ100をクエリ';

  @override
  String get plateModalPreviousPage => '前のページ';

  @override
  String get plateModalNextPage => '次のページ';

  // Progress bar
  @override
  String get progressLabel => '進行状況';

  @override
  String get availableLabel => '利用可能';

  @override
  String get terminateButton => '終了';

  // History screen
  @override
  String get historyTitle => '履歴';

  @override
  String get historyEmpty => '検索履歴がありません';

  @override
  String get historyEmptyFiltered => '現在のフィルターで結果が見つかりません';

  @override
  String get historySort => 'ソート';

  @override
  String get historyFilter => 'フィルター';

  @override
  String get historyClear => 'クリア';

  @override
  String get historyStats => '統計';

  @override
  String get historyAll => 'すべて';

  @override
  String get historyDateDesc => '日付（新しい順）';

  @override
  String get historyDateAsc => '日付（古い順）';

  @override
  String get historyPlateNumber => 'プレート番号';

  @override
  String get historyAvailableFirst => '利用可能を最初に';

  @override
  String get historyUnavailableFirst => '利用不可を最初に';

  @override
  String get historyTotal => '合計';

  // History context menu
  @override
  String get plateDetailsTitle => 'プレート詳細';

  @override
  String get plateNumberLabel => 'プレート番号';

  @override
  String get plateTypeLabel => 'プレートタイプ';

  @override
  String get vehicleTypeContextLabel => '車両タイプ';

  @override
  String get availabilityLabel => '利用可能性';

  @override
  String get lastQueryTimeLabel => '最後のクエリ';

  @override
  String get closeButton => '閉じる';

  @override
  String get retryButton => '再試行';

  @override
  String get availableStatus => '利用可能';

  @override
  String get unavailableStatus => '利用不可';

  @override
  String get styleUnavailableMessage => 'プレートは利用可能ですが、スタイルが利用できない可能性があります、0294331600にお電話ください';

  // Language switching
  @override
  String get languageSwitchTooltip => '言語を変更';

  @override
  String get systemLanguage => 'システム';

  @override
  String get chineseLanguage => '中国語';

  @override
  String get englishLanguage => '英語';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'クエリ完了';

  @override
  String get queryFailedMessage => 'クエリ失敗';

  // Favorites
  @override
  String get favoritesTitle => 'お気に入り';

  @override
  String get favoritesTooltip => 'お気に入り';

  @override
  String get favoriteAdd => 'お気に入りに追加';

  @override
  String get favoriteRemove => 'お気に入りから削除';

  @override
  String get favoriteAddedMessage => 'お気に入りに追加されました';

  @override
  String get favoriteRemovedMessage => 'お気に入りから削除されました';

  @override
  String get favoritesEmpty => 'お気に入りレコードがありません';

  // Favorite status
  @override
  String get favoriteStatusLabel => 'お気に入り状態';

  @override
  String get favoriteStatusFavorited => 'お気に入り済み';

  @override
  String get favoriteStatusNotFavorited => 'お気に入り未登録';

  // Help screen
  @override
  String get helpTooltip => 'ヘルプ';

  @override
  String get helpTitle => 'ヘルプドキュメント';

  @override
  String get helpIntroTitle => 'アプリ紹介';

  @override
  String get helpIntroContent => 'MyPlate VIC は、ニューサウスウェールズ州のナンバープレート番号の利用可能性を確認するために特別に設計されたアプリケーションです。';

  @override
  String get helpFeaturesTitle => '機能';

  @override
  String get helpKeywordTitle => 'キーワード入力';

  @override
  String get helpKeywordContent => 'ナンバープレートに含めたい文字や数字を入力してください。';

  @override
  String get helpModeTitle => 'モード選択';

  @override
  String get helpModeContent => 'プレートでキーワードが表示される場所を選択：プレフィックス、サフィックス、または任意の場所。';

  @override
  String get helpVehicleTypeTitle => '車両タイプ';

  @override
  String get helpVehicleTypeContent => '車両タイプを選択：自動車またはオートバイ。異なるタイプには異なるプレートフォーマットとルールがあります。';

  @override
  String get helpDigitsTitle => '桁数選択';

  @override
  String get helpDigitsContent => 'ナンバープレートの総桁数を選択してください。';

  @override
  String get helpProcessTitle => 'クエリプロセス';

  @override
  String get helpProcessStep1 => '1. キーワードを入力し、関連パラメータを選択';

  @override
  String get helpProcessStep2 => '2. 「プレート組み合わせを生成」をクリックして、すべての可能な組み合わせを表示';

  @override
  String get helpProcessStep3 => '3. ポップアップリストからクエリしたいプレートを選択';

  @override
  String get helpProcessStep4 => '4. 「クエリ開始」をクリックして、バッチ利用可能性チェックを開始';

  @override
  String get helpProcessStep5 => '5. 完了後、結果を表示するために履歴ページに自動的に移動';

  @override
  String get helpHistoryTitle => '履歴記録';

  @override
  String get helpHistoryContent => 'すべてのクエリ結果は履歴記録に保存されます。各プレートの利用可能性ステータスを確認できます。';

  @override
  String get helpFavoritesTitle => 'お気に入り機能';

  @override
  String get helpFavoritesContent => '興味深いプレート番号をお気に入りリストに追加できます。履歴カードの右上角にあるハートアイコンをクリックしてお気に入りに追加するか、詳細メニューのお気に入りオプションを使用してください。お気に入りのプレートは専用のお気に入りページで表示・管理できます。';

  @override
  String get helpFaqTitle => 'よくある質問';

  @override
  String get helpFaqQuestion1 => 'Q: なぜ一部のプレートが利用不可として表示されるのですか？';

  @override
  String get helpFaqAnswer1 => 'A: プレートは既に他の人によって登録されている可能性があります。';

  @override
  String get helpFaqQuestion2 => 'Q: クエリにはどのくらい時間がかかりますか？';

  @override
  String get helpFaqAnswer2 => 'A: クエリ時間は選択されたプレートの数によって異なります。';

  @override
  String get helpFaqQuestion3 => 'Q: 同時にクエリできるプレートは何枚ですか？';

  @override
  String get helpFaqAnswer3 => 'A: 一度に最大100枚のプレートをクエリ用に選択できます。';

  @override
  String get helpFaqQuestion4 => 'Q: 履歴記録はどのくらい保持されますか？';

  @override
  String get helpFaqAnswer4 => 'A: 履歴記録はデバイスに永続的に保存されます。';

  @override
  String get helpContactTitle => 'お問い合わせ';

  @override
  String get helpContactContent => '問題や提案がある場合は、アプリストアのレビューを通じてお問い合わせください。';

  // Common
  @override
  String get available => '利用可能';

  @override
  String get unavailable => '利用不可';

  @override
  String get custom => 'カスタム';

  @override
  String get personalised => 'パーソナライズ';

  @override
  String get digits => '桁';

  @override
  String get loading => '読み込み中';

  @override
  String get error => 'エラー';

  @override
  String get retry => '再試行';

  @override
  String get close => '閉じる';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';
}
