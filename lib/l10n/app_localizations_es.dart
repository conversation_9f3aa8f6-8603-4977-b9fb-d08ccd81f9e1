import 'app_localizations.dart';

class AppLocalizationsEs extends AppLocalizations {
  @override
  String get appTitle => 'MyPlates VIC';

  // Home screen
  @override
  String get homeTitle => 'MyPlates VIC';

  @override
  String get historyTooltip => 'Historial';

  @override
  String get keywordLabel => 'Palabra clave (Opcional)';

  @override
  String get keywordHint => 'Ingrese palabra clave o deje vacío para todas las combinaciones';

  @override
  String get modeLabel => 'Modo';

  @override
  String get vehicleTypeLabel => 'Tipo de vehículo';

  @override
  String get digitsLabel => 'Seleccionar dígitos';

  @override
  String get generatePlatesButton => 'Generar combinaciones de placas';

  @override
  String get enterKeywordError => 'Por favor ingrese una palabra clave';

  @override
  String get selectDigitsError => 'Por favor seleccione el número de dígitos';

  @override
  String get selectDigitsHint => 'Por favor seleccione el número de dígitos';

  @override
  String get searchingText => 'Buscando...';

  @override
  String get terminatingText => 'Terminando...';

  @override
  String get searchCompleteText => '¡Búsqueda completada! {} placas disponibles encontradas';

  @override
  String get searchTerminatedText => 'Búsqueda cancelada por el usuario';

  @override
  String get confirmTerminateTitle => 'Confirmar terminación';

  @override
  String get confirmTerminateContent => '¿Está seguro de que desea terminar la búsqueda en curso?';

  @override
  String get cancelButton => 'Cancelar';

  @override
  String get confirmTerminateButton => 'Terminar';

  // Keyword modes
  @override
  String get keywordModePrefix => 'Prefijo';

  @override
  String get keywordModeSuffix => 'Sufijo';

  @override
  String get keywordModeAnywhere => 'En cualquier lugar';

  @override
  String get keywordModePrefixDesc => 'La palabra clave aparece al principio';

  @override
  String get keywordModeSuffixDesc => 'La palabra clave aparece al final';

  @override
  String get keywordModeAnywhereDesc => 'La palabra clave puede aparecer en cualquier lugar';

  // Vehicle types
  @override
  String get vehicleTypeCar => 'Coche';

  @override
  String get vehicleTypeMotorcycle => 'Motocicleta';

  // Plate modal
  @override
  String get plateModalTitle => 'Resultados de placas';

  @override
  String get plateModalLoading => 'Cargando...';

  @override
  String get plateModalError => 'Error al cargar las placas';

  @override
  String get plateModalRetry => 'Reintentar';

  @override
  String get plateModalSelectAll => 'Seleccionar todo';

  @override
  String get plateModalDeselectAll => 'Deseleccionar todo';

  @override
  String get plateModalStartQuery => 'Iniciar consulta';

  @override
  String get plateModalClose => 'Cerrar';

  @override
  String get plateModalSelected => 'seleccionado(s)';

  @override
  String get plateModalMaxSelection => 'Se pueden seleccionar máximo {} placas';

  @override
  String get plateModalConcurrency => 'Concurrencia';

  @override
  String get plateModalCopiedToClipboard => 'Copiado al portapapeles';

  @override
  String get plateModalGenerationFailed => 'Falló la generación de placas';

  @override
  String get plateModalNoPlatesWithFilter => 'No se encontraron placas con los filtros actuales';

  @override
  String get plateModalCurrentPage => 'Página actual';

  @override
  String get plateModalNoHistory => 'Sin historial';

  @override
  String get plateModalUnavailable => 'No disponible';

  @override
  String get plateModalClearSelection => 'Limpiar selección';

  @override
  String get plateModalFilter => 'Filtrar';

  @override
  String get plateModalSort => 'Ordenar';

  @override
  String get plateModalTotal => 'Total';

  @override
  String get plateModalFiltered => 'Filtrado';

  @override
  String get plateModalHasHistory => 'Con historial';

  @override
  String get plateModalNoHistoryFirst => 'Sin historial primero';

  @override
  String get plateModalAlphabetical => 'Alfabético';

  @override
  String get plateModalSelectPlates => 'Seleccionar placas';

  @override
  String get plateModalQueryAll => 'Consultar Top 100';

  @override
  String get plateModalPreviousPage => 'Página anterior';

  @override
  String get plateModalNextPage => 'Página siguiente';

  // Progress bar
  @override
  String get progressLabel => 'Progreso';

  @override
  String get availableLabel => 'Disponible';

  @override
  String get terminateButton => 'Terminar';

  // History screen
  @override
  String get historyTitle => 'Historial';

  @override
  String get historyEmpty => 'Sin historial de búsqueda';

  @override
  String get historyEmptyFiltered => 'No se encontraron resultados con los filtros actuales';

  @override
  String get historySort => 'Ordenar';

  @override
  String get historyFilter => 'Filtrar';

  @override
  String get historyClear => 'Limpiar';

  @override
  String get historyStats => 'Estadísticas';

  @override
  String get historyAll => 'Todo';

  @override
  String get historyDateDesc => 'Fecha (más reciente primero)';

  @override
  String get historyDateAsc => 'Fecha (más antiguo primero)';

  @override
  String get historyPlateNumber => 'Número de placa';

  @override
  String get historyAvailableFirst => 'Disponibles primero';

  @override
  String get historyUnavailableFirst => 'No disponibles primero';

  @override
  String get historyTotal => 'Total';

  // History context menu
  @override
  String get plateDetailsTitle => 'Detalles de la placa';

  @override
  String get plateNumberLabel => 'Número de placa';

  @override
  String get plateTypeLabel => 'Tipo de placa';

  @override
  String get vehicleTypeContextLabel => 'Tipo de vehículo';

  @override
  String get availabilityLabel => 'Disponibilidad';

  @override
  String get lastQueryTimeLabel => 'Última consulta';

  @override
  String get favoriteStatusLabel => 'Estado de favoritos';

  @override
  String get favoriteStatusFavorited => 'Favorito';

  @override
  String get favoriteStatusNotFavorited => 'No favorito';

  @override
  String get closeButton => 'Cerrar';

  @override
  String get retryButton => 'Reintentar';

  @override
  String get availableStatus => 'Disponible';

  @override
  String get unavailableStatus => 'No disponible';

  @override
  String get styleUnavailableMessage => 'Matrícula disponible, pero el estilo puede no estar disponible, por favor llame al 0294331600';

  // Language switching
  @override
  String get languageSwitchTooltip => 'Cambiar idioma';

  @override
  String get systemLanguage => 'Sistema';

  @override
  String get chineseLanguage => 'Chino';

  @override
  String get englishLanguage => 'Inglés';

  // History screen notifications
  @override
  String get queryCompleteMessage => 'Consulta completada';

  @override
  String get queryFailedMessage => 'Consulta fallida';

  // Favorites
  @override
  String get favoritesTitle => 'Favoritos';

  @override
  String get favoritesTooltip => 'Favoritos';

  @override
  String get favoriteAdd => 'Agregar a favoritos';

  @override
  String get favoriteRemove => 'Quitar de favoritos';

  @override
  String get favoriteAddedMessage => 'Agregado a favoritos';

  @override
  String get favoriteRemovedMessage => 'Quitado de favoritos';

  @override
  String get favoritesEmpty => 'No hay registros favoritos';

  // Help screen
  @override
  String get helpTooltip => 'Ayuda';

  @override
  String get helpTitle => 'Documentación de ayuda';

  @override
  String get helpIntroTitle => 'Introducción de la aplicación';

  @override
  String get helpIntroContent => 'MyPlate VIC es una aplicación diseñada específicamente para verificar la disponibilidad de números de matrícula de Nueva Gales del Sur.';

  @override
  String get helpFeaturesTitle => 'Características';

  @override
  String get helpKeywordTitle => 'Entrada de palabra clave';

  @override
  String get helpKeywordContent => 'Ingrese las letras o números que desea incluir en su matrícula.';

  @override
  String get helpModeTitle => 'Selección de modo';

  @override
  String get helpModeContent => 'Elija dónde aparece la palabra clave en la matrícula: prefijo, sufijo o en cualquier lugar.';

  @override
  String get helpVehicleTypeTitle => 'Tipo de vehículo';

  @override
  String get helpVehicleTypeContent => 'Seleccione su tipo de vehículo: coche o motocicleta. Diferentes tipos tienen diferentes formatos y reglas de placas.';

  @override
  String get helpDigitsTitle => 'Selección de dígitos';

  @override
  String get helpDigitsContent => 'Elija el número total de dígitos para la matrícula.';

  @override
  String get helpProcessTitle => 'Proceso de consulta';

  @override
  String get helpProcessStep1 => '1. Ingrese la palabra clave y seleccione los parámetros relacionados';

  @override
  String get helpProcessStep2 => '2. Haga clic en "Generar combinaciones de matrícula" para ver todas las combinaciones posibles';

  @override
  String get helpProcessStep3 => '3. Seleccione las matrículas que desea consultar de la lista emergente';

  @override
  String get helpProcessStep4 => '4. Haga clic en "Iniciar consulta" para comenzar la verificación de disponibilidad por lotes';

  @override
  String get helpProcessStep5 => '5. Después de completarse, navegue automáticamente a la página de historial para ver los resultados';

  @override
  String get helpHistoryTitle => 'Registros de historial';

  @override
  String get helpHistoryContent => 'Todos los resultados de consulta se guardan en registros de historial. Puede ver el estado de disponibilidad de cada matrícula.';

  @override
  String get helpFavoritesTitle => 'Función de Favoritos';

  @override
  String get helpFavoritesContent => 'Puede agregar números de matrícula interesantes a su lista de favoritos. Haga clic en el ícono de corazón en la esquina superior derecha de las tarjetas de historial para marcarlas como favoritas, o use la opción de favoritos en el menú de detalles. Las matrículas favoritas se pueden ver y gestionar en la página dedicada de favoritos.';

  @override
  String get helpFaqTitle => 'Preguntas frecuentes';

  @override
  String get helpFaqQuestion1 => 'P: ¿Por qué algunas matrículas aparecen como no disponibles?';

  @override
  String get helpFaqAnswer1 => 'R: La matrícula puede estar ya registrada por otra persona.';

  @override
  String get helpFaqQuestion2 => 'P: ¿Cuánto tiempo toma la consulta?';

  @override
  String get helpFaqAnswer2 => 'R: El tiempo de consulta depende del número de matrículas seleccionadas.';

  @override
  String get helpFaqQuestion3 => 'P: ¿Cuántas matrículas se pueden consultar simultáneamente?';

  @override
  String get helpFaqAnswer3 => 'R: Se puede seleccionar un máximo de 100 matrículas para consulta a la vez.';

  @override
  String get helpFaqQuestion4 => 'P: ¿Cuánto tiempo se mantienen los registros de historial?';

  @override
  String get helpFaqAnswer4 => 'R: Los registros de historial se guardan permanentemente en su dispositivo.';

  @override
  String get helpContactTitle => 'Contáctanos';

  @override
  String get helpContactContent => 'Si tiene problemas o sugerencias, contáctenos a través de las reseñas de la tienda de aplicaciones.';

  // Common
  @override
  String get available => 'Disponible';

  @override
  String get unavailable => 'No disponible';

  @override
  String get custom => 'Personalizado';

  @override
  String get personalised => 'Personalizado';

  @override
  String get digits => 'dígitos';

  @override
  String get loading => 'Cargando';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Reintentar';

  @override
  String get close => 'Cerrar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get confirm => 'Confirmar';
}
