import 'package:flutter/material.dart';
import 'dart:async';

/// 通知管理器 - 单例模式
/// 实现覆盖模式的通知显示，新通知会立即覆盖旧通知并重新计时
/// 通知从顶部滑出，支持自定义动画效果
class NotificationManager {
  static NotificationManager? _instance;

  /// 当前显示的Overlay条目
  OverlayEntry? _currentOverlay;

  /// 当前的计时器
  Timer? _currentTimer;

  /// 动画控制器
  AnimationController? _animationController;

  /// 私有构造函数
  NotificationManager._();

  /// 获取单例实例
  static NotificationManager get instance {
    _instance ??= NotificationManager._();
    return _instance!;
  }
  
  /// 显示通知消息（覆盖模式）
  ///
  /// [context] BuildContext
  /// [message] 通知消息内容
  /// [duration] 显示时长，默认4秒
  /// [backgroundColor] 背景颜色，可选
  /// [textColor] 文字颜色，可选
  /// [icon] 可选的图标
  void showNotification(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
  }) {
    // 如果有正在显示的通知，立即关闭它
    _dismissCurrent();

    // 获取Overlay
    final overlay = Overlay.of(context);

    // 查找TickerProvider
    TickerProvider? tickerProvider;
    try {
      // 尝试从Scaffold获取TickerProvider
      tickerProvider = Scaffold.of(context);
    } catch (e) {
      // 如果没有Scaffold，尝试查找NavigatorState
      try {
        final navigator = Navigator.of(context);
        tickerProvider = navigator;
      } catch (e) {
        // 如果都没有，使用简单版本
        debugPrint('Warning: No TickerProvider found, showing notification without animation');
        _showSimpleNotification(context, message, duration, backgroundColor, textColor, icon);
        return;
      }
    }

    // 创建动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      reverseDuration: const Duration(milliseconds: 250),
      vsync: tickerProvider,
    );

    // 创建滑动动画
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1), // 从上方开始
      end: Offset.zero, // 滑到正常位置
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeOutCubic,
      reverseCurve: Curves.easeInCubic,
    ));

    // 创建透明度动画
    final opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeOut,
      reverseCurve: Curves.easeIn,
    ));

    // 创建Overlay条目
    _currentOverlay = OverlayEntry(
      builder: (context) => _buildNotificationWidget(
        message: message,
        backgroundColor: backgroundColor,
        textColor: textColor,
        icon: icon,
        slideAnimation: slideAnimation,
        opacityAnimation: opacityAnimation,
      ),
    );

    // 插入Overlay
    overlay.insert(_currentOverlay!);

    // 开始进入动画
    _animationController!.forward();

    // 设置自动关闭计时器
    _currentTimer = Timer(duration, () {
      _dismissWithAnimation();
    });
  }
  
  /// 构建通知Widget
  Widget _buildNotificationWidget({
    required String message,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    required Animation<Offset> slideAnimation,
    required Animation<double> opacityAnimation,
  }) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SlideTransition(
        position: slideAnimation,
        child: FadeTransition(
          opacity: opacityAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 50, 16, 0), // 顶部留出状态栏空间
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: backgroundColor ?? Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      color: textColor ?? Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Text(
                      message,
                      style: TextStyle(
                        color: textColor ?? Colors.black87,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _dismissWithAnimation(),
                    child: Icon(
                      Icons.close,
                      color: (textColor ?? Colors.black87).withValues(alpha: 0.7),
                      size: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示成功消息
  void showSuccess(BuildContext context, String message, {Duration? duration}) {
    showNotification(
      context,
      message,
      duration: duration ?? const Duration(seconds: 3),
      backgroundColor: Colors.green,
      textColor: Colors.white,
      icon: Icons.check_circle,
    );
  }
  
  /// 显示错误消息
  void showError(BuildContext context, String message, {Duration? duration}) {
    showNotification(
      context,
      message,
      duration: duration ?? const Duration(seconds: 5),
      backgroundColor: Colors.red,
      textColor: Colors.white,
      icon: Icons.error,
    );
  }

  /// 显示警告消息
  void showWarning(BuildContext context, String message, {Duration? duration}) {
    showNotification(
      context,
      message,
      duration: duration ?? const Duration(seconds: 4),
      backgroundColor: Colors.orange,
      textColor: Colors.white,
      icon: Icons.warning,
    );
  }

  /// 显示信息消息
  void showInfo(BuildContext context, String message, {Duration? duration}) {
    showNotification(
      context,
      message,
      duration: duration ?? const Duration(seconds: 4),
      backgroundColor: Colors.blue,
      textColor: Colors.white,
      icon: Icons.info,
    );
  }
  
  /// 带动画关闭当前通知
  void _dismissWithAnimation() {
    if (_animationController != null && _currentOverlay != null) {
      _animationController!.reverse().then((_) {
        _dismissCurrent();
      });
    }
  }

  /// 立即关闭当前显示的通知
  void _dismissCurrent() {
    // 取消当前计时器
    _currentTimer?.cancel();
    _currentTimer = null;

    // 移除Overlay条目
    if (_currentOverlay != null) {
      _currentOverlay!.remove();
      _currentOverlay = null;
    }

    // 释放动画控制器
    if (_animationController != null) {
      _animationController!.dispose();
      _animationController = null;
    }
  }

  /// 手动关闭所有通知
  void dismissAll() {
    _dismissWithAnimation();
  }

  /// 检查是否有通知正在显示
  bool get hasActiveNotification => _currentOverlay != null;

  /// 显示简单通知（无动画版本）
  /// 用于在没有TickerProvider时的后备方案
  void _showSimpleNotification(
    BuildContext context,
    String message,
    Duration duration,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
  ) {
    // 如果有正在显示的通知，立即关闭它
    _dismissCurrent();

    // 获取Overlay
    final overlay = Overlay.of(context);

    // 创建Overlay条目（无动画）
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.black87,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: textColor ?? Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    message,
                    style: TextStyle(
                      color: textColor ?? Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => _dismissCurrent(),
                  child: Icon(
                    Icons.close,
                    color: (textColor ?? Colors.white).withValues(alpha: 0.7),
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // 插入Overlay
    overlay.insert(_currentOverlay!);

    // 设置计时器自动关闭
    _currentTimer = Timer(duration, () {
      _dismissCurrent();
    });
  }
}
