
import '../enums/keyword_mode.dart';

/// 车牌号码生成工具类
class PlateGenerator {
  /// 生成包含关键词的车牌号码组合
  ///
  /// [keyword] 必须包含的关键词
  /// [digits] 车牌总位数 (1-6位)
  /// [mode] 关键词位置模式
  /// [limit] 最多返回的组合数量，默认为100
  static List<String> generatePlates({
    required String keyword,
    required int digits,
    required KeywordMode mode,
    int limit = 100,
  }) {
    // 验证输入参数
    if (digits < 1 || digits > 6) {
      throw ArgumentError('车牌位数必须在1-6位之间');
    }

    if (keyword.length > digits) {
      throw ArgumentError('关键词长度不能超过车牌总位数');
    }

    final List<String> results = [];
    final Set<String> generatedPlates = {}; // 用于去重

    // 字符集：大写字母和数字
    const String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final String upperKeyword = keyword.toUpperCase();

    // 如果关键词已经等于总位数，则只有一种组合
    if (keyword.length == digits) {
      return [upperKeyword];
    }

    // 计算剩余位数
    final int remainingChars = digits - keyword.length;

    // 根据模式生成车牌
    switch (mode) {
      case KeywordMode.prefix:
        // 前缀模式：关键词在开头
        _generateWithPrefix(
          chars: chars,
          keyword: upperKeyword,
          remainingChars: remainingChars,
          results: results,
          generatedPlates: generatedPlates,
          limit: limit,
        );
        break;

      case KeywordMode.suffix:
        // 后缀模式：关键词在结尾
        _generateWithSuffix(
          chars: chars,
          keyword: upperKeyword,
          remainingChars: remainingChars,
          results: results,
          generatedPlates: generatedPlates,
          limit: limit,
        );
        break;

      case KeywordMode.anywhere:
        // 任意模式：关键词可以在任意位置
        // 为每个位置分配相等的限制数量
        final int positionCount = remainingChars + 1;
        final int limitPerPosition = (limit / positionCount).ceil();

        for (int position = 0; position <= remainingChars; position++) {
          final int currentResultCount = results.length;
          _generateForPosition(
            chars: chars,
            keyword: upperKeyword,
            position: position,
            digits: digits,
            results: results,
            generatedPlates: generatedPlates,
            limit: currentResultCount + limitPerPosition,
          );

          // 如果已达到总限制，停止生成
          if (results.length >= limit) {
            break;
          }
        }
        break;
    }

    return results;
  }

  /// 生成前缀模式的车牌（关键词在开头）
  static void _generateWithPrefix({
    required String chars,
    required String keyword,
    required int remainingChars,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    _generateSuffixOnly(
      chars: chars,
      prefix: keyword, // 关键词作为前缀
      currentSuffix: '',
      suffixLength: remainingChars,
      results: results,
      generatedPlates: generatedPlates,
      limit: limit,
    );
  }

  /// 生成后缀模式的车牌（关键词在结尾）
  static void _generateWithSuffix({
    required String chars,
    required String keyword,
    required int remainingChars,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    _generatePrefixOnly(
      chars: chars,
      keyword: keyword,
      currentPrefix: '',
      prefixLength: remainingChars,
      results: results,
      generatedPlates: generatedPlates,
      limit: limit,
    );
  }

  /// 生成纯后缀组合（用于前缀模式）
  static void _generateSuffixOnly({
    required String chars,
    required String prefix,
    required String currentSuffix,
    required int suffixLength,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    // 如果已达到限制，停止生成
    if (results.length >= limit) {
      return;
    }

    // 如果后缀已经达到指定长度
    if (currentSuffix.length == suffixLength) {
      final String plate = prefix + currentSuffix;

      // 添加到结果集
      if (!generatedPlates.contains(plate)) {
        generatedPlates.add(plate);
        results.add(plate);
      }

      return;
    }

    // 为后缀添加每个可能的字符
    for (int i = 0; i < chars.length; i++) {
      final String newSuffix = currentSuffix + chars[i];
      _generateSuffixOnly(
        chars: chars,
        prefix: prefix,
        currentSuffix: newSuffix,
        suffixLength: suffixLength,
        results: results,
        generatedPlates: generatedPlates,
        limit: limit,
      );
    }
  }

  /// 生成纯前缀组合（用于后缀模式）
  static void _generatePrefixOnly({
    required String chars,
    required String keyword,
    required String currentPrefix,
    required int prefixLength,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    // 如果已达到限制，停止生成
    if (results.length >= limit) {
      return;
    }

    // 如果前缀已经达到指定长度
    if (currentPrefix.length == prefixLength) {
      final String plate = currentPrefix + keyword;

      // 添加到结果集
      if (!generatedPlates.contains(plate)) {
        generatedPlates.add(plate);
        results.add(plate);
      }

      return;
    }

    // 为前缀添加每个可能的字符
    for (int i = 0; i < chars.length; i++) {
      final String newPrefix = currentPrefix + chars[i];
      _generatePrefixOnly(
        chars: chars,
        keyword: keyword,
        currentPrefix: newPrefix,
        prefixLength: prefixLength,
        results: results,
        generatedPlates: generatedPlates,
        limit: limit,
      );
    }
  }

  /// 为特定位置生成所有可能的组合
  static void _generateForPosition({
    required String chars,
    required String keyword,
    required int position,
    required int digits,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    final int remainingChars = digits - keyword.length;
    
    // 生成位置前的字符组合
    _generatePrefixCombinations(
      chars: chars,
      keyword: keyword,
      position: position,
      remainingChars: remainingChars,
      currentPrefix: '',
      results: results,
      generatedPlates: generatedPlates,
      limit: limit,
    );
  }
  
  /// 递归生成前缀组合
  static void _generatePrefixCombinations({
    required String chars,
    required String keyword,
    required int position,
    required int remainingChars,
    required String currentPrefix,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    // 如果已达到限制，停止生成
    if (results.length >= limit) {
      return;
    }
    
    // 如果前缀已经达到指定长度
    if (currentPrefix.length == position) {
      // 计算后缀长度
      final int suffixLength = remainingChars - position;
      
      // 生成所有可能的后缀
      _generateSuffixCombinations(
        chars: chars,
        keyword: keyword,
        prefix: currentPrefix,
        currentSuffix: '',
        suffixLength: suffixLength,
        results: results,
        generatedPlates: generatedPlates,
        limit: limit,
      );
      
      return;
    }
    
    // 为前缀添加每个可能的字符
    for (int i = 0; i < chars.length; i++) {
      final String newPrefix = currentPrefix + chars[i];
      _generatePrefixCombinations(
        chars: chars,
        keyword: keyword,
        position: position,
        remainingChars: remainingChars,
        currentPrefix: newPrefix,
        results: results,
        generatedPlates: generatedPlates,
        limit: limit,
      );
    }
  }
  
  /// 递归生成后缀组合
  static void _generateSuffixCombinations({
    required String chars,
    required String keyword,
    required String prefix,
    required String currentSuffix,
    required int suffixLength,
    required List<String> results,
    required Set<String> generatedPlates,
    required int limit,
  }) {
    // 如果已达到限制，停止生成
    if (results.length >= limit) {
      return;
    }
    
    // 如果后缀已经达到指定长度
    if (currentSuffix.length == suffixLength) {
      final String plate = prefix + keyword + currentSuffix;
      
      // 添加到结果集
      if (!generatedPlates.contains(plate)) {
        generatedPlates.add(plate);
        results.add(plate);
      }
      
      return;
    }
    
    // 为后缀添加每个可能的字符
    for (int i = 0; i < chars.length; i++) {
      final String newSuffix = currentSuffix + chars[i];
      _generateSuffixCombinations(
        chars: chars,
        keyword: keyword,
        prefix: prefix,
        currentSuffix: newSuffix,
        suffixLength: suffixLength,
        results: results,
        generatedPlates: generatedPlates,
        limit: limit,
      );
    }
  }
  
  /// 验证车牌号码格式
  /// 
  /// [plate] 要验证的车牌号码
  /// 返回是否有效
  static bool validatePlate(String plate) {
    // 验证长度 (1-6位)
    if (plate.isEmpty || plate.length > 6) {
      return false;
    }
    
    // 验证字符 (只允许大写字母和数字)
    final RegExp validChars = RegExp(r'^[A-Z0-9]+$');
    return validChars.hasMatch(plate);
  }
  
  /// 判断车牌类型
  /// 
  /// [plate] 车牌号码
  /// 返回车牌类型 ("Personalised" 或 "Custom")
  static String getPlateType(String plate) {
    // 根据NSW车牌规则判断类型
    // Personalised类型的正则表达式模式
    final List<RegExp> personalisedPatterns = [
      RegExp(r'^[A-Z]{2}\d{2}[A-Z]{2}$'),  // 两个字母+两个数字+两个字母
      RegExp(r'^[A-Z]{2}\d{3,4}$'),        // 两个字母+3-4个数字
      RegExp(r'^[A-Z]{3}\d{3}$'),          // 三个字母+三个数字
      RegExp(r'^[A-Z]{3}\d{2}[A-Z]$'),     // 三个字母+两个数字+一个字母
      RegExp(r'^\d{2}[A-Z]{3,4}$'),        // 两个数字+3-4个字母
      RegExp(r'^\d{3}[A-Z]{3}$'),          // 三个数字+三个字母
    ];
    
    // 检查是否匹配任一Personalised模式
    for (final pattern in personalisedPatterns) {
      if (pattern.hasMatch(plate)) {
        return 'Personalised';
      }
    }
    
    // 不匹配任何Personalised模式，则为Custom
    return 'Custom';
  }
}