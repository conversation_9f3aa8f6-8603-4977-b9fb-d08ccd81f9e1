import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

/// 单例Logger类，用于替代print函数
/// 在初始化时根据运行环境决定是否输出日志，避免重复检查环境
class Logger {
  static Logger? _instance;
  
  /// 日志输出函数，根据环境在初始化时确定
  late final void Function(Object?) _logFunction;
  
  /// 私有构造函数
  Logger._() {
    // 在初始化时检查环境并设置相应的日志函数
    if (kDebugMode) {
      // Debug环境：使用dart:developer的log函数避免循环引用
      _logFunction = (Object? message) {
        developer.log(message?.toString() ?? 'null', name: 'Logger');
      };
    } else {
      // Release或Profile环境：不做任何操作
      _logFunction = (Object? message) {
        // 空操作，不输出任何内容
      };
    }
  }
  
  /// 获取Logger单例实例
  static Logger get instance {
    _instance ??= Logger._();
    return _instance!;
  }
  
  /// 日志输出方法，替代print函数
  /// 
  /// [message] 要输出的消息
  void log(Object? message) {
    _logFunction(message);
  }
  
  /// 便捷的静态方法，直接调用单例的log方法
  /// 
  /// [message] 要输出的消息
  static void print(Object? message) {
    instance.log(message);
  }
  
  /// 输出调试信息
  /// 
  /// [message] 调试消息
  /// [tag] 可选的标签，用于标识日志来源
  void debug(Object? message, [String? tag]) {
    if (tag != null) {
      _logFunction('[DEBUG][$tag] $message');
    } else {
      _logFunction('[DEBUG] $message');
    }
  }
  
  /// 输出信息日志
  /// 
  /// [message] 信息消息
  /// [tag] 可选的标签，用于标识日志来源
  void info(Object? message, [String? tag]) {
    if (tag != null) {
      _logFunction('[INFO][$tag] $message');
    } else {
      _logFunction('[INFO] $message');
    }
  }
  
  /// 输出警告日志
  /// 
  /// [message] 警告消息
  /// [tag] 可选的标签，用于标识日志来源
  void warning(Object? message, [String? tag]) {
    if (tag != null) {
      _logFunction('[WARNING][$tag] $message');
    } else {
      _logFunction('[WARNING] $message');
    }
  }
  
  /// 输出错误日志
  /// 
  /// [message] 错误消息
  /// [tag] 可选的标签，用于标识日志来源
  void error(Object? message, [String? tag]) {
    if (tag != null) {
      _logFunction('[ERROR][$tag] $message');
    } else {
      _logFunction('[ERROR] $message');
    }
  }
  
  /// 检查当前是否为调试模式
  bool get isDebugMode => kDebugMode;
  
  /// 检查当前是否启用日志输出
  bool get isLoggingEnabled => kDebugMode;
}
