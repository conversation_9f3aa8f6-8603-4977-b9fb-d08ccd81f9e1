import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/api_logger.dart';
import '../utils/notification_manager.dart';

class ApiLogScreen extends StatefulWidget {
  const ApiLogScreen({super.key});

  @override
  State<ApiLogScreen> createState() => _ApiLogScreenState();
}

class _ApiLogScreenState extends State<ApiLogScreen> {
  String _logContent = '';
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final logs = await ApiLogger.readLogs();
      setState(() {
        _logContent = logs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _logContent = '加载日志失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清空'),
        content: const Text('确定要清空所有API日志吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ApiLogger.clearLogs();
      await _loadLogs();
      if (mounted) {
        NotificationManager.instance.showSuccess(
          context,
          '日志已清空',
        );
      }
    }
  }

  Future<void> _copyLogs() async {
    await Clipboard.setData(ClipboardData(text: _logContent));
    if (mounted) {
      NotificationManager.instance.showSuccess(
        context,
        '日志已复制到剪贴板',
      );
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API请求日志'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogs,
            tooltip: '刷新日志',
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyLogs,
            tooltip: '复制日志',
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearLogs,
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 信息栏
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '日志文件路径: ${ApiLogger.getLogFilePath() ?? "未知"}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                Text(
                  '提示: 此日志包含所有API请求的详细信息，可帮助诊断网络问题',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          // 日志内容
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _logContent.isEmpty
                    ? const Center(
                        child: Text(
                          '暂无日志记录\n\n请先进行一些API请求操作',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.all(16),
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          child: SelectableText(
                            _logContent,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
          ),
        ],
      ),
      
      // 浮动按钮
      floatingActionButton: _logContent.isNotEmpty
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                FloatingActionButton.small(
                  heroTag: 'scroll_top',
                  onPressed: _scrollToTop,
                  tooltip: '滚动到顶部',
                  child: const Icon(Icons.keyboard_arrow_up),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'scroll_bottom',
                  onPressed: _scrollToBottom,
                  tooltip: '滚动到底部',
                  child: const Icon(Icons.keyboard_arrow_down),
                ),
              ],
            )
          : null,
    );
  }
}
