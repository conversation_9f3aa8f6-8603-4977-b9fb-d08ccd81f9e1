import 'package:flutter/material.dart';
import '../widgets/record_list_widget.dart';
import '../l10n/app_localizations.dart';

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.historyTitle,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE3F2FD), // 浅天蓝色
              Color(0xFF1976D2), // 深天蓝色
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: const RecordListWidget(
            showFilters: true, // 历史页面显示过滤器和排序选项
            showFavoritesOnly: false, // 显示所有记录
          ),
        ),
      ),
    );
  }
}
