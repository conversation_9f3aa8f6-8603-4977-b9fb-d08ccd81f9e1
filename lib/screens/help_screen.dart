import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.helpTitle),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App Introduction
            _buildSection(
              context,
              localizations.helpIntroTitle,
              localizations.helpIntroContent,
              Icons.info_outline,
            ),
            
            const SizedBox(height: 24),
            
            // Features
            _buildSection(
              context,
              localizations.helpFeaturesTitle,
              '',
              Icons.star_outline,
            ),
            
            const SizedBox(height: 16),
            
            // Feature subsections
            _buildSubSection(
              context,
              localizations.helpKeywordTitle,
              localizations.helpKeywordContent,
              Icons.keyboard,
            ),
            
            const SizedBox(height: 16),
            
            _buildSubSection(
              context,
              localizations.helpModeTitle,
              localizations.helpModeContent,
              Icons.tune,
            ),
            
            const SizedBox(height: 16),
            
            _buildSubSection(
              context,
              localizations.helpVehicleTypeTitle,
              localizations.helpVehicleTypeContent,
              Icons.directions_car,
            ),
            
            const SizedBox(height: 16),
            
            _buildSubSection(
              context,
              localizations.helpDigitsTitle,
              localizations.helpDigitsContent,
              Icons.pin,
            ),
            
            const SizedBox(height: 24),
            
            // Query Process
            _buildSection(
              context,
              localizations.helpProcessTitle,
              '',
              Icons.play_circle_outline,
            ),
            
            const SizedBox(height: 16),
            
            _buildProcessSteps(context, localizations),
            
            const SizedBox(height: 24),
            
            // History
            _buildSection(
              context,
              localizations.helpHistoryTitle,
              localizations.helpHistoryContent,
              Icons.history,
            ),

            const SizedBox(height: 24),

            // Favorites
            _buildSection(
              context,
              localizations.helpFavoritesTitle,
              localizations.helpFavoritesContent,
              Icons.favorite,
            ),

            const SizedBox(height: 24),
            
            // FAQ
            _buildSection(
              context,
              localizations.helpFaqTitle,
              '',
              Icons.help_outline,
            ),
            
            const SizedBox(height: 16),
            
            _buildFaqSection(context, localizations),
            
            const SizedBox(height: 24),
            
            // Contact
            _buildSection(
              context,
              localizations.helpContactTitle,
              localizations.helpContactContent,
              Icons.contact_support,
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            if (content.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                content,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubSection(BuildContext context, String title, String content, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0),
      child: Card(
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: Theme.of(context).colorScheme.secondary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                content,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProcessSteps(BuildContext context, AppLocalizations localizations) {
    final steps = [
      localizations.helpProcessStep1,
      localizations.helpProcessStep2,
      localizations.helpProcessStep3,
      localizations.helpProcessStep4,
      localizations.helpProcessStep5,
    ];

    return Padding(
      padding: const EdgeInsets.only(left: 16.0),
      child: Card(
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: steps.map((step) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      step,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildFaqSection(BuildContext context, AppLocalizations localizations) {
    final faqs = [
      {
        'question': localizations.helpFaqQuestion1,
        'answer': localizations.helpFaqAnswer1,
      },
      {
        'question': localizations.helpFaqQuestion2,
        'answer': localizations.helpFaqAnswer2,
      },
      {
        'question': localizations.helpFaqQuestion3,
        'answer': localizations.helpFaqAnswer3,
      },
      {
        'question': localizations.helpFaqQuestion4,
        'answer': localizations.helpFaqAnswer4,
      },
    ];

    return Padding(
      padding: const EdgeInsets.only(left: 16.0),
      child: Column(
        children: faqs.map((faq) => Card(
          elevation: 1,
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ExpansionTile(
            title: Text(
              faq['question']!,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  faq['answer']!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        )).toList(),
      ),
    );
  }
}
