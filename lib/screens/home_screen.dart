import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../widgets/plate_modal.dart';
import '../models/plate_number.dart';
import '../providers/search_provider.dart';
import '../providers/locale_provider.dart';
import '../widgets/progress_bar.dart';
import '../enums/keyword_mode.dart';
import '../enums/vehicle_type.dart';
import '../l10n/app_localizations.dart';
import '../utils/notification_manager.dart';
import 'history_screen.dart';
import 'favorites_screen.dart';
import 'api_log_screen.dart';
import 'help_screen.dart';
import '../test/notification_test_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // 并发数设置：Debug模式下为3，Release模式下为2
  static int get concurrency => kDebugMode ? 3 : 2;
  
  final TextEditingController _keywordController = TextEditingController();
  int? _selectedDigits; // 去掉默认值，用户必须选择
  KeywordMode _selectedMode = KeywordMode.anywhere; // 默认任意模式
  VehicleType _selectedVehicleType = VehicleType.car; // 默认汽车

  // 自动跳转相关
  Timer? _autoNavigationTimer;
  bool _wasSearching = false;

  @override
  void dispose() {
    _keywordController.dispose();
    _autoNavigationTimer?.cancel();
    super.dispose();
  }

  // 根据车辆类型获取最大字符限制
  int get _maxKeywordLength {
    return _selectedVehicleType == VehicleType.motorcycle ? 5 : 6;
  }

  void _showPlateModal() async {
    final localizations = AppLocalizations.of(context);
    
    // 检查是否选择了字符数量
    if (_selectedDigits == null) {
      NotificationManager.instance.showError(
        context,
        localizations.selectDigitsError,
      );
      return;
    }

    // 显示模态框并等待结果
    final List<PlateNumber>? selectedPlates =
        await showModalBottomSheet<List<PlateNumber>>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => PlateModal(
        keyword: _keywordController.text.trim(),
        digits: _selectedDigits!,
        mode: _selectedMode,
        vehicleType: _selectedVehicleType,
      ),
    );

    // 如果用户选择了"开始查询"并返回了车牌列表
    if (selectedPlates != null &&
        selectedPlates.isNotEmpty &&
        context.mounted) {
      // 获取本地化对象和车辆类型显示名称
      final localizations = AppLocalizations.of(context);
      final vehicleDisplayName = _selectedVehicleType.displayName(context);
      
      // 获取provider引用在async gap之前
      final searchProvider = context.read<SearchProvider>();
      // 开始查询
      await searchProvider.startSearch(
            plates: selectedPlates,
            concurrency: concurrency, // 使用定义的并发数变量
            vehicleType: _selectedVehicleType,
            vehicleDisplayName: vehicleDisplayName,
          );
    }
  }

  void _openHistoryScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HistoryScreen()),
    );
  }

  void _openFavoritesScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FavoritesScreen()),
    );
  }

  void _openApiLogScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ApiLogScreen()),
    );
  }

  void _openHelpScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HelpScreen()),
    );
  }

  void _openNotificationTestPage() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationTestPage()),
    );
  }

  /// 创建紧凑的图标按钮
  Widget _buildCompactIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return SizedBox(
      width: 32, // 固定宽度，减少占用空间
      height: 40,
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: EdgeInsets.zero, // 完全移除内边距
        constraints: const BoxConstraints(), // 移除最小尺寸约束
      ),
    );
  }

  /// 创建紧凑的弹出菜单按钮
  Widget _buildCompactPopupMenuButton(AppLocalizations localizations) {
    return SizedBox(
      width: 32, // 固定宽度，减少占用空间
      height: 40,
      child: PopupMenuButton<String>(
        icon: const Icon(Icons.language, size: 20),
        tooltip: localizations.languageSwitchTooltip,
        padding: EdgeInsets.zero, // 完全移除内边距 
        constraints: const BoxConstraints(), // 移除最小尺寸约束
        onSelected: (String languageCode) {
          context.read<LocaleProvider>().setLocale(languageCode);
        },
        itemBuilder: (BuildContext context) {
          final localeProvider = context.read<LocaleProvider>();
          final languages = localeProvider.getAvailableLanguages();

          return languages.map((language) {
            return PopupMenuItem<String>(
              value: language.code,
              child: Row(
                children: [
                  Icon(
                    language.isSelected ? Icons.check : Icons.language,
                    size: 20,
                    color: language.isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          language.nativeName,
                          style: TextStyle(
                            fontWeight: language.isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                        if (language.name != language.nativeName)
                          Text(
                            language.name,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList();
        },
      ),
    );
  }

  /// 检查搜索状态变化并处理自动跳转
  void _handleSearchStateChange(SearchProvider provider) {
    // 检测搜索从进行中变为完成状态
    if (_wasSearching && !provider.isSearching && provider.completedQueries > 0) {
      // 取消之前的定时器（如果存在）
      _autoNavigationTimer?.cancel();

      // 设置1秒延迟后自动跳转到历史页面
      _autoNavigationTimer = Timer(const Duration(seconds: 1), () {
        if (mounted) {
          _openHistoryScreen();
        }
      });
    }

    // 更新搜索状态
    _wasSearching = provider.isSearching;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false, // 不显示默认的leading
        actions: [
          // 历史页面
          _buildCompactIconButton(
            icon: Icons.history,
            onPressed: _openHistoryScreen,
            tooltip: localizations.historyTooltip,
          ),
          // 收藏页面
          _buildCompactIconButton(
            icon: Icons.favorite,
            onPressed: _openFavoritesScreen,
            tooltip: localizations.favoritesTooltip,
          ),
          // 帮助页面
          _buildCompactIconButton(
            icon: Icons.help_outline,
            onPressed: _openHelpScreen,
            tooltip: localizations.helpTooltip,
          ),
          // Debug日志 - 仅在Debug模式下显示
          if (kDebugMode)
            _buildCompactIconButton(
              icon: Icons.bug_report,
              onPressed: _openApiLogScreen,
              tooltip: 'API日志',
            ),
          // 通知测试 - 仅在Debug模式下显示
          if (kDebugMode)
            _buildCompactIconButton(
              icon: Icons.notifications,
              onPressed: _openNotificationTestPage,
              tooltip: '通知测试',
            ),
          // 语言切换
          _buildCompactPopupMenuButton(localizations),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE3F2FD), // 浅天蓝色
              Color(0xFF1976D2), // 深天蓝色
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 40), // 添加顶部间距
                // 居中显示的应用标题
                Center(
                  child: Text(
                    localizations.homeTitle,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 2),
                          blurRadius: 4,
                          color: Colors.black26,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                // 输入表单区域使用白色背景
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        offset: const Offset(0, 4),
                        blurRadius: 8,
                        color: Colors.black.withValues(alpha: 0.1),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            flex: 1,
                            child: TextField(
                              controller: _keywordController,
                              maxLength: _maxKeywordLength,
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(_maxKeywordLength),
                                FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                              ],
                              decoration: InputDecoration(
                                labelText: localizations.keywordLabel,
                                hintText: localizations.keywordHint,
                                border: const OutlineInputBorder(),
                                prefixIcon: const Icon(Icons.search),
                                counterText: '', // 隐藏字符计数器
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            flex: 1,
                            child: DropdownButtonFormField<KeywordMode>(
                              value: _selectedMode,
                              decoration: InputDecoration(
                                labelText: localizations.modeLabel,
                                border: const OutlineInputBorder(),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                              ),
                              items: KeywordMode.values
                                  .map((mode) => DropdownMenuItem(
                                        value: mode,
                                        child: Text(mode.displayName(context)),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedMode = value;
                                  });
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<VehicleType>(
                        value: _selectedVehicleType,
                        decoration: InputDecoration(
                          labelText: localizations.vehicleTypeLabel,
                          border: const OutlineInputBorder(),
                        ),
                        items: VehicleType.values
                            .map((vehicleType) => DropdownMenuItem(
                                  value: vehicleType,
                                  child: Text(vehicleType.displayName(context)),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedVehicleType = value;
                              // 当车辆类型改变时，检查关键字长度是否超出限制
                              final currentKeyword = _keywordController.text;
                              if (currentKeyword.length > _maxKeywordLength) {
                                _keywordController.text = currentKeyword.substring(0, _maxKeywordLength);
                              }
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<int>(
                        value: _selectedDigits,
                        decoration: InputDecoration(
                          labelText: localizations.digitsLabel,
                          border: const OutlineInputBorder(),
                        ),
                        hint: Text(localizations.selectDigitsHint),
                        items: List.generate(_maxKeywordLength, (index) => index + 1)
                            .map((digit) => DropdownMenuItem(
                                  value: digit,
                                  child: Text('$digit${localizations.digits}'),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedDigits = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _showPlateModal,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: Colors.lightBlue,
                          foregroundColor: Colors.white,
                          elevation: 4,
                          shadowColor: Colors.lightBlue.withValues(alpha: 0.4),
                        ),
                        child: Text(
                          localizations.generatePlatesButton,
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                // 显示查询进度
                Consumer<SearchProvider>(
                  builder: (context, provider, child) {
                    // 处理搜索状态变化和自动跳转
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _handleSearchStateChange(provider);
                    });

                    if (provider.isSearching) {
                      return Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              offset: const Offset(0, 4),
                              blurRadius: 8,
                              color: Colors.black.withValues(alpha: 0.1),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            SearchProgressBar(
                              progress: provider.progress,
                              completed: provider.completedQueries,
                              total: provider.totalQueries,
                              available: provider.availablePlates,
                              isSearching: true,
                              onCancel: () {
                                // 显示确认对话框
                                showDialog(
                                  context: context,
                                  builder: (BuildContext dialogContext) {
                                    final dialogLocalizations = AppLocalizations.of(dialogContext);
                                    return AlertDialog(
                                      title: Text(dialogLocalizations.confirmTerminateTitle),
                                      content: Text(dialogLocalizations.confirmTerminateContent),
                                      actions: [
                                        TextButton(
                                          onPressed: () => Navigator.of(dialogContext).pop(),
                                          child: Text(dialogLocalizations.cancelButton),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            Navigator.of(dialogContext).pop();
                                            provider.cancelSearch();
                                          },
                                          child: Text(dialogLocalizations.confirmTerminateButton),
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                            ),
                            const SizedBox(height: 16),
                            Text(
                              provider.isCancelled ? localizations.terminatingText : localizations.searchingText,
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (provider.completedQueries > 0) {
                      return Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              offset: const Offset(0, 4),
                              blurRadius: 8,
                              color: Colors.black.withValues(alpha: 0.1),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            SearchProgressBar(
                              progress: provider.progress,
                              completed: provider.completedQueries,
                              total: provider.totalQueries,
                              available: provider.availablePlates,
                              isSearching: false,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              provider.isCancelled
                                ? localizations.searchTerminatedText.replaceAll('{}', '${provider.availablePlates}')
                                : localizations.searchCompleteText.replaceAll('{}', '${provider.availablePlates}'),
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
