import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// 关键词位置模式枚举
enum KeywordMode {
  /// 前缀模式 - 关键词必须出现在开头
  prefix,

  /// 后缀模式 - 关键词必须出现在结尾
  suffix,

  /// 任意模式 - 关键词可以出现在任意位置
  anywhere;

  /// 获取显示名称
  String displayName(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case KeywordMode.prefix:
        return localizations.keywordModePrefix;
      case KeywordMode.suffix:
        return localizations.keywordModeSuffix;
      case KeywordMode.anywhere:
        return localizations.keywordModeAnywhere;
    }
  }

  /// 获取描述
  String description(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case KeywordMode.prefix:
        return localizations.keywordModePrefixDesc;
      case KeywordMode.suffix:
        return localizations.keywordModeSuffixDesc;
      case KeywordMode.anywhere:
        return localizations.keywordModeAnywhereDesc;
    }
  }
}
