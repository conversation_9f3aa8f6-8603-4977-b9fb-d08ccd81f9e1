import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// 车辆类型枚举
enum VehicleType {
  car('CAR'),
  motorcycle('MOTORCYCLE');

  const VehicleType(this.apiValue);

  /// API请求中使用的值
  final String apiValue;

  /// 获取小写的值
  String get lowercaseValue {
    switch (this) {
      case VehicleType.car:
        return 'car';
      case VehicleType.motorcycle:
        return 'motorcycle';
    }
  }

  /// 获取显示名称
  String displayName(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case VehicleType.car:
        return localizations.vehicleTypeCar;
      case VehicleType.motorcycle:
        return localizations.vehicleTypeMotorcycle;
    }
  }

  /// 从API值或显示名称字符串获取车辆类型枚举
  static VehicleType? fromString(String? value) {
    if (value == null) return null;

    // 首先尝试匹配API值
    for (VehicleType type in VehicleType.values) {
      if (type.apiValue == value) {
        return type;
      }
    }

    // 然后尝试匹配中文显示名称（用于向后兼容）
    switch (value) {
      case '重型车辆':
        return VehicleType.car;
      case '摩托车':
        return VehicleType.motorcycle;
      default:
        return null;
    }
  }

  /// 获取本地化的车辆类型显示名称，如果无法解析则返回原始字符串
  static String getLocalizedDisplayName(BuildContext context, String? vehicleTypeString) {
    if (vehicleTypeString == null) return '';

    final vehicleType = fromString(vehicleTypeString);
    if (vehicleType != null) {
      return vehicleType.displayName(context);
    }

    // 如果无法解析，返回原始字符串
    return vehicleTypeString;
  }
}
