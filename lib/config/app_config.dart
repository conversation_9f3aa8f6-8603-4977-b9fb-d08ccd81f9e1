import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../utils/logger.dart';

class AppConfig {
  static const String apiBaseUrl = 'https://vplates.com.au/vplatesapi/checkcombo';
  static const String refererUrl = 'https://vplates.com.au/create/check-combination';

  /// 获取基于设备的用户代理字符串
  static Future<String> getUserAgent() async {
    if (kIsWeb) {
      return _getWebUserAgent();
    }

    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return _getAndroidUserAgent(androidInfo);
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return _getIOSUserAgent(iosInfo);
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        return _getWindowsUserAgent(windowsInfo);
      } else if (Platform.isMacOS) {
        final macOSInfo = await deviceInfo.macOsInfo;
        return _getMacOSUserAgent(macOSInfo);
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        return _getLinuxUserAgent(linuxInfo);
      }
    } catch (e) {
      Logger.instance.warning('获取设备信息失败，使用默认用户代理: $e', 'AppConfig');
    }

    // 默认用户代理（如果无法获取设备信息）
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }

  /// Web平台用户代理
  static String _getWebUserAgent() {
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }

  /// Android平台用户代理
  static String _getAndroidUserAgent(AndroidDeviceInfo androidInfo) {
    final version = androidInfo.version.release;
    final model = androidInfo.model;
    final brand = androidInfo.brand;

    return 'Mozilla/5.0 (Linux; Android $version; $brand $model) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
  }

  /// iOS平台用户代理
  static String _getIOSUserAgent(IosDeviceInfo iosInfo) {
    final version = iosInfo.systemVersion.replaceAll('.', '_');
    final model = iosInfo.model;

    return 'Mozilla/5.0 ($model; CPU OS $version like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
  }

  /// Windows平台用户代理
  static String _getWindowsUserAgent(WindowsDeviceInfo windowsInfo) {
    final version = windowsInfo.majorVersion;
    final build = windowsInfo.buildNumber;

    return 'Mozilla/5.0 (Windows NT $version.$build; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }

  /// macOS平台用户代理
  static String _getMacOSUserAgent(MacOsDeviceInfo macOSInfo) {
    final version = macOSInfo.majorVersion;
    final minorVersion = macOSInfo.minorVersion;

    return 'Mozilla/5.0 (Macintosh; Intel Mac OS X ${version}_$minorVersion) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }

  /// Linux平台用户代理
  static String _getLinuxUserAgent(LinuxDeviceInfo linuxInfo) {
    return 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }
}
