import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// Configuration constants for history screen
class HistoryConfig {
  // Grid layout
  static const double cardAspectRatio = 2.5; // Width to height ratio for cards (更高的比例 = 更矮的卡片)
  static const double cardSpacing = 3.0; // Spacing between cards (进一步减少间距)
  static const double cardMinWidth = 100.0; // Minimum card width (减小最小宽度)
  static const double cardMinHeight = 50.0; // Minimum card height (最小卡片高度)

  // Context menu
  static const double contextMenuWidth = 200.0;
  static const double contextMenuItemHeight = 48.0;

  // Calculate items per page based on screen dimensions
  static int getItemsPerPage(double screenWidth, double screenHeight) {
    final columns = getCardsPerRow(screenWidth);

    // 估算可用高度（减去AppBar、过滤器、统计条、分页控制等）
    // 减少预留空间，因为卡片更矮了
    final availableHeight = screenHeight - 320; // 减少到320px

    // 估算每个卡片的高度（包括间距）
    final cardWidth = (screenWidth - 24 - (columns - 1) * cardSpacing) / columns; // 减少边距到24px
    final calculatedCardHeight = cardWidth / cardAspectRatio;
    
    // 应用最小高度限制
    final cardHeight = calculatedCardHeight < cardMinHeight ? cardMinHeight : calculatedCardHeight;
    final itemHeightWithSpacing = cardHeight + cardSpacing;

    // 计算可以显示的行数（向下取整，宁愿少一行也不要超出）
    final maxRows = (availableHeight / itemHeightWithSpacing).floor();

    // 确保至少显示1行，但如果计算出的可用高度太小，则使用最小值
    final rows = maxRows > 0 ? maxRows : 1;

    // 确保是列数的倍数
    final itemsPerPage = rows * columns;

    // 设置最小值，确保至少显示一行
    return itemsPerPage >= columns ? itemsPerPage : columns;
  }

  // Calculate cards per row based on screen width (更激进的列数范围：2-4列)
  static int getCardsPerRow(double screenWidth) {
    if (screenWidth < 230) {
      return 1; // 超小屏幕: 1列
    } else if (screenWidth < 340) {
      return 2; // 小屏幕: 2列
    } else if (screenWidth < 450) {
      return 3; // 小屏幕: 3列
    } else if (screenWidth < 600) {
      return 4; // 中等屏幕: 4列
    } else if (screenWidth < 700) {
      return 5; // 大屏幕: 5列
    } else if (screenWidth < 800) {
      return 6; // 超大屏幕: 6列
    } else if (screenWidth < 900) {
      return 7; // 超大屏幕: 7列
    } else {
      return 8; // 超大屏幕: 8列
    }
  }

  // Calculate actual aspect ratio considering minimum height constraint
  static double getActualAspectRatio(double screenWidth) {
    final columns = getCardsPerRow(screenWidth);
    final cardWidth = (screenWidth - 24 - (columns - 1) * cardSpacing) / columns;
    final calculatedCardHeight = cardWidth / cardAspectRatio;
    
    // Apply minimum height constraint
    final actualCardHeight = calculatedCardHeight < cardMinHeight ? cardMinHeight : calculatedCardHeight;
    
    // Return actual aspect ratio
    return cardWidth / actualCardHeight;
  }
}

// Filter options for history
enum HistoryFilterOption {
  all,
  custom,
  personalised,
  available,
  unavailable;

  String getLabel(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case HistoryFilterOption.all:
        return localizations.historyAll;
      case HistoryFilterOption.custom:
        return 'Custom';
      case HistoryFilterOption.personalised:
        return 'Personalised';
      case HistoryFilterOption.available:
        return localizations.available;
      case HistoryFilterOption.unavailable:
        return localizations.unavailable;
    }
  }
}

// Sort options for history
enum HistorySortOption {
  dateDesc,
  dateAsc,
  plateNumber,
  availableFirst,
  unavailableFirst,
  typePersonalisedFirst,
  typeCustomFirst;

  String getLabel(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case HistorySortOption.dateDesc:
        return localizations.historyDateDesc;
      case HistorySortOption.dateAsc:
        return localizations.historyDateAsc;
      case HistorySortOption.plateNumber:
        return localizations.historyPlateNumber;
      case HistorySortOption.availableFirst:
        return localizations.historyAvailableFirst;
      case HistorySortOption.unavailableFirst:
        return localizations.historyUnavailableFirst;
      case HistorySortOption.typePersonalisedFirst:
        return 'Personalised';
      case HistorySortOption.typeCustomFirst:
        return 'Custom';
    }
  }
}
