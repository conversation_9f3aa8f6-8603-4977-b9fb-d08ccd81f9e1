import requests
import itertools
import string
import time
import random
import csv
import json

# --- 配置 ---
BASE_URL = "https://myrta.com/plateapi/plate/"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Referer': 'https://www.myplates.com.au/create-plate'
}
PARAMS = {
    'vehicleType': 'LIGHT_VEHICLE'
}
OUTPUT_CSV_FILE = 'plate_availability_results.csv'
MIN_DELAY_SECONDS = 0
MAX_DELAY_SECONDS = 1
COMBINATION_LENGTH = 2

# --- 主逻辑 ---
def get_plate_availability(plate_combination):
    """
    获取指定车牌组合的可用性。
    特别处理404状态码，因为根据用户描述，这可能表示车牌不可用但仍返回JSON。

    Args:
        plate_combination (str): 要查询的车牌字符组合。

    Returns:
        bool or None: 如果请求成功且JSON有效，则返回 'available' 的布尔值。
                      如果发生不可恢复的错误或JSON无效，则返回 None。
    """
    url = f"{BASE_URL}{plate_combination}"
    try:
        response = requests.get(url, headers=HEADERS, params=PARAMS, timeout=15) # 略微增加超时

        # 检查状态码
        # 如果是 2xx (成功) 或 404 (根据用户描述，这表示 "不可用" 但有JSON响应)
        if response.status_code // 100 == 2 or response.status_code == 404:
            try:
                data = response.json()
                availability_info = data.get('plateAvailability', {})
                if availability_info is None: # 处理 plateAvailability 键不存在或为 null 的情况
                    print(f"警告 - 组合: {plate_combination}, URL: {response.url}, 状态码: {response.status_code} - 'plateAvailability' 键不存在或为 null。响应内容: {response.text[:200]}...")
                    return None # 或者可以根据具体情况返回一个特定的错误标识
                
                available = availability_info.get('available')
                if available is None: # 处理 available 键不存在的情况
                     print(f"警告 - 组合: {plate_combination}, URL: {response.url}, 状态码: {response.status_code} - 'available' 键在 'plateAvailability' 中不存在。响应内容: {response.text[:200]}...")
                     return None
                
                # 即使是404，如果 available 字段存在，我们也接受它
                if response.status_code == 404 and available is False:
                    print(f"信息 - 组合: {plate_combination}, URL: {response.url}, 状态码: 404 - 确认为不可用。")
                elif response.status_code == 404 and available is True:
                     print(f"警告 - 组合: {plate_combination}, URL: {response.url}, 状态码: 404 但 'available' 为 True。这与预期不符。响应内容: {response.text[:200]}...")
                     # 根据实际情况决定如何处理，这里暂时当作有效数据处理
                
                return available

            except json.JSONDecodeError as json_err:
                print(f"JSON 解析错误 - 组合: {plate_combination}, URL: {response.url}, 状态码: {response.status_code}: {json_err}. 响应内容: {response.text[:200]}...")
                return None
        else:
            # 对于其他非2xx且非404的错误状态码，认为是真正的HTTP错误
            print(f"HTTP 错误 - 组合: {plate_combination}, URL: {response.url}, 状态码: {response.status_code} - {response.reason}. 响应内容: {response.text[:200]}...")
            if response.status_code == 403:
                print("  收到403 Forbidden错误。可能是IP被暂时阻止或请求过于频繁。建议增加延迟或检查网络配置。")
            # 可以根据需要添加对其他特定状态码的处理
            return None

    except requests.exceptions.ConnectionError as conn_err:
        print(f"连接错误 - 组合: {plate_combination}: {conn_err}")
        return None
    except requests.exceptions.Timeout as timeout_err:
        print(f"请求超时 - 组合: {plate_combination}: {timeout_err}")
        return None
    except requests.exceptions.RequestException as req_err: # 捕获其他 requests 库的异常
        print(f"请求发生错误 - 组合: {plate_combination}: {req_err}")
        return None
    except Exception as e: # 捕获其他潜在的未知错误
        print(f"发生未知错误 - 组合: {plate_combination}: {e}")
        return None


def generate_combinations(length):
    """
    生成指定长度的字母和数字的组合。

    Args:
        length (int): 组合的长度。

    Returns:
        iterator: 返回一个包含所有字符组合的迭代器。
    """
    characters = string.ascii_uppercase + string.digits
    for item in itertools.product(characters, repeat=length):
        yield "".join(item)

def main():
    """
    主函数，执行整个流程。
    """
    print(f"开始查询车牌可用性，结果将保存到 {OUTPUT_CSV_FILE}...")
    processed_count = 0
    error_count = 0
    success_count = 0
    not_available_404_count = 0 # 记录通过404确认为不可用的次数

    processed_combinations = set()
    file_exists = False
    try:
        with open(OUTPUT_CSV_FILE, mode='r', newline='', encoding='utf-8') as f_read:
            reader = csv.reader(f_read)
            try:
                header = next(reader) # 尝试读取表头
                if header == ['Combination', 'Available']: # 检查表头是否符合预期
                    for row in reader:
                        if row and len(row) > 0: # 确保行不为空且有内容
                            processed_combinations.add(row[0])
                else:
                    print(f"警告: {OUTPUT_CSV_FILE} 的表头 '{header}' 与预期 ['Combination', 'Available'] 不符。将从头开始或检查文件内容。")
                    # 可以选择清空 processed_combinations 或采取其他措施
                    processed_combinations.clear() # 为安全起见，如果表头不对，则重新开始
            except StopIteration: # 文件为空
                pass # 文件为空，processed_combinations 保持为空
        if processed_combinations:
            print(f"从 {OUTPUT_CSV_FILE} 中加载了 {len(processed_combinations)} 个已处理的组合。")
        file_exists = True
    except FileNotFoundError:
        print(f"{OUTPUT_CSV_FILE} 不存在，将创建一个新文件。")
    except Exception as e:
        print(f"读取已处理组合时发生错误: {e}。将从头开始。")
        processed_combinations.clear()


    with open(OUTPUT_CSV_FILE, mode='a', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile)
        # 如果文件是新创建的，或者虽然存在但没有加载任何已处理组合（可能因为表头问题或文件为空），则写入表头
        if not file_exists or not processed_combinations and not csvfile.tell(): # csvfile.tell() == 0 表示文件指针在开头，即文件为空或新文件
            csv_writer.writerow(['Combination', 'Available'])
            csvfile.flush()

        combinations_generator = generate_combinations(COMBINATION_LENGTH)
        total_combinations = (len(string.ascii_uppercase) + len(string.digits)) ** COMBINATION_LENGTH
        print(f"总共需要检查 {total_combinations} 个组合。")


        for i, combination in enumerate(combinations_generator):
            if combination in processed_combinations:
                # print(f"跳过已处理的组合: {combination}")
                continue

            print(f"\n正在查询组合 {i+1}/{total_combinations}: {combination}...")
            availability = get_plate_availability(combination)

            if availability is not None:
                csv_writer.writerow([combination, availability])
                csvfile.flush()
                print(f"  结果: {combination} -> {'可用' if availability else '不可用'}")
                success_count += 1
                # if availability is False and get_plate_availability.__last_status_code == 404: # 假设我们能获取到最后的状态码
                #     not_available_404_count +=1
            else:
                csv_writer.writerow([combination, 'Error'])
                csvfile.flush()
                print(f"  查询组合 {combination} 时发生错误或无法解析。")
                error_count +=1

            processed_count += 1
            delay = random.uniform(MIN_DELAY_SECONDS, MAX_DELAY_SECONDS)
            print(f"  等待 {delay:.2f} 秒...")
            time.sleep(delay)

    print("\n--- 处理完成 ---")
    print(f"总共尝试处理的组合数: {processed_count}")
    print(f"成功查询并记录的组合数 (包括通过404确认不可用的): {success_count}")
    print(f"其中，通过404状态码确认为不可用的数量: {not_available_404_count}") # 这部分逻辑需要稍微调整get_plate_availability才能精确记录
    print(f"查询失败或无法解析的组合数: {error_count}")
    print(f"所有结果已保存到 {OUTPUT_CSV_FILE}")

# 为了能够记录是否因为404导致不可用，我们可以稍微修改 get_plate_availability
# 的返回，或者使用一个全局变量或类成员来传递这个信息。
# 这里采用修改函数签名的方式，使其可以返回状态码，但为了保持简单，
# 我会在 main 函数中对 get_plate_availability 的打印信息进行一些说明。
# 如果需要精确统计404导致的不可用，可以考虑让 get_plate_availability 返回一个元组 (availability, status_code)
# 或者在 get_plate_availability 内部设置一个可访问的属性来记录最后的状态码。

# 简化的方式：在 get_plate_availability 的打印信息中已经指出了404的情况。
# 精确统计 not_available_404_count 需要更复杂的交互，
# 但主要功能（正确处理404并提取 available:false）已经实现。

if __name__ == "__main__":
    main()