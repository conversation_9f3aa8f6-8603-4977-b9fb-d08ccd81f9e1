# 手动测试指南 - Home页面关键字输入框改进

## 测试目标
验证以下两个新功能：
1. 关键字输入框根据车辆类型动态调整字符限制（摩托车5个字符，其他6个字符）
2. 字符数量dropdown没有默认值，用户必须选择

## 测试步骤

### 测试1：字符限制功能

#### 步骤1：验证默认状态（轻型车辆）
1. 启动应用
2. 确认车辆类型默认选择为"轻型车辆"
3. 在关键字输入框中输入字符
4. **预期结果**：最多可以输入6个字符

#### 步骤2：切换到摩托车
1. 点击车辆类型下拉框
2. 选择"摩托车"
3. 在关键字输入框中输入字符
4. **预期结果**：最多可以输入5个字符

#### 步骤3：验证字符截断
1. 在轻型车辆模式下输入6个字符（如"ABCDEF"）
2. 切换到摩托车类型
3. **预期结果**：关键字自动截断为5个字符（"ABCDE"）

#### 步骤4：切换回其他车辆类型
1. 从摩托车切换到其他任意车辆类型（如拖车）
2. **预期结果**：字符限制恢复为6个字符

### 测试2：字符数量选择验证

#### 步骤1：验证默认状态
1. 启动应用
2. 查看"选择位数"下拉框
3. **预期结果**：显示提示文本"请选择号码长度"，没有预选值

#### 步骤2：验证必须选择
1. 输入关键字（如"ABC"）
2. 不选择字符数量
3. 点击"生成车牌组合"按钮
4. **预期结果**：显示错误提示"请选择号码长度"

#### 步骤3：验证摩托车选项限制
1. 选择车辆类型为"摩托车"
2. 点击"选择位数"下拉框
3. **预期结果**：只显示1-5位选项

#### 步骤4：验证其他车辆类型选项
1. 选择车辆类型为"轻型车辆"
2. 点击"选择位数"下拉框
3. **预期结果**：显示1-6位选项

### 测试3：完整流程验证

#### 摩托车完整流程
1. 选择车辆类型：摩托车
2. 输入关键字：ABC（3个字符）
3. 选择位数：5位
4. 点击"生成车牌组合"
5. **预期结果**：成功生成车牌组合

#### 轻型车辆完整流程
1. 选择车辆类型：轻型车辆
2. 输入关键字：ABCDEF（6个字符）
3. 选择位数：6位
4. 点击"生成车牌组合"
5. **预期结果**：成功生成车牌组合

## 错误情况测试

### 测试4：边界条件

#### 关键字长度超过选择位数
1. 输入关键字：ABCDE（5个字符）
2. 选择位数：3位
3. 点击"生成车牌组合"
4. **预期结果**：应该显示相应的错误提示

#### 空关键字
1. 不输入关键字
2. 选择位数：5位
3. 点击"生成车牌组合"
4. **预期结果**：显示"请输入关键词"错误提示

## 验证清单

- [ ] 默认车辆类型为轻型车辆，字符限制为6
- [ ] 摩托车类型字符限制为5
- [ ] 其他车辆类型字符限制为6
- [ ] 切换车辆类型时自动截断超长关键字
- [ ] 字符数量下拉框没有默认值
- [ ] 显示"请选择号码长度"提示文本
- [ ] 未选择字符数量时显示错误提示
- [ ] 摩托车类型只显示1-5位选项
- [ ] 其他车辆类型显示1-6位选项
- [ ] 完整流程可以正常生成车牌

## 注意事项

1. 测试时注意观察UI反馈和错误提示
2. 确保在不同车辆类型之间切换时功能正常
3. 验证字符输入限制是实时生效的
4. 检查本地化文本是否正确显示（中文/英文）
