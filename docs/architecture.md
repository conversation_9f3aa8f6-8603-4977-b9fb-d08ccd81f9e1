# MyPlate VIC 应用架构设计

## 1. 项目概述

MyPlate VIC 是一个用于查询新南威尔士州车牌号码可用性的移动应用。该应用允许用户根据特定规则生成车牌号码组合，并检查这些号码的可用性。

## 2. 技术栈

- **框架**: Flutter
- **状态管理**: Provider
- **本地数据库**: Sembast
- **HTTP客户端**: Dio
- **UI组件**: Flutter Material Design

## 3. 项目结构

```
lib/
├── main.dart                 # 应用入口
├── config/                   # 配置文件
│   └── app_config.dart      # 应用配置
├── models/                   # 数据模型
│   ├── plate_number.dart    # 车牌号码模型
│   └── search_record.dart   # 搜索记录模型
├── services/                 # 服务层
│   ├── api_service.dart     # API服务
│   └── database_service.dart # 数据库服务
├── providers/               # 状态管理
│   ├── plate_provider.dart  # 车牌相关状态
│   └── search_provider.dart # 搜索相关状态
├── screens/                 # 页面
│   ├── home_screen.dart     # 主页
│   └── history_screen.dart  # 历史记录页
├── widgets/                 # 可复用组件
│   ├── plate_modal.dart     # 车牌组合模态框
│   └── progress_bar.dart    # 进度条组件
└── utils/                   # 工具类
    ├── plate_generator.dart # 车牌生成工具
    └── constants.dart       # 常量定义
```

## 4. 核心功能模块

### 4.1 车牌号码生成与验证
- 支持输入关键词生成车牌号码
- 验证车牌号码格式（1-6位长度）
- 区分 Personalised formats 和 Custom formats

### 4.2 数据库管理
- 使用 Sembast 存储搜索记录
- 记录包含：车牌号码、可用性状态、查询时间戳
- 支持记录的增删改查操作

### 4.3 并发查询
- 支持1-3个并发查询
- 使用异步任务管理查询队列
- 实时更新查询进度

### 4.4 用户界面
- 主页：规则输入和查询控制
- 历史记录页：查看已查询记录
- 模态框：显示生成的组合列表
- 进度条：显示查询进度

## 5. 数据流

1. 用户输入关键词，并指定号码位数
2. 生成包含关键词的指定位数的车牌号码组合
3. 显示模态框，展示组合列表
4. 用户选择并发数量并启动查询
5. 系统并发执行查询任务
6. 关闭模态框，显示带有数量的进度条，并实时更新进度条
7. 将查询结果保存到数据库
8. 在历史记录页面显示查询结果

## 6. 安全考虑

- 实现请求限流
- 错误处理和重试机制
- 数据持久化备份

## 7. 性能优化

- 使用并发查询提高效率
- 实现本地缓存
- 优化数据库查询性能

## 8. 未来扩展

- 支持更多车牌格式
- 添加导出功能
- 实现用户偏好设置
- 添加通知功能 