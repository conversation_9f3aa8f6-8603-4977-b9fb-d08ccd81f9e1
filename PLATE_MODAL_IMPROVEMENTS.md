# Plate Modal 功能改进说明

## 概述

对 `PlateModal` 进行了全面改进，参考 history 页面的设计模式，增加了分页、过滤、选择、批量操作和排序等功能，大大提升了处理大量车牌组合的能力。

## 新增功能

### 1. 配置常量（参考 history 页面设计）
- `PlateModalConfig.maxSelectionCount = 100`: 最大选择数量
- `PlateModalConfig.maxGenerateLimit = 1000`: 最大生成数量
- `PlateModalConfig.cardAspectRatio = 2.0`: 卡片宽高比
- `PlateModalConfig.cardSpacing = 4.0`: 卡片间距
- `PlateModalConfig.cardMinWidth = 100.0`: 最小卡片宽度

### 2. 动态布局系统（参考 history 页面）
- **动态列数计算**: 根据屏幕宽度自动调整列数（小屏3列，中屏4列，大屏5列）
- **动态每页数量**: 根据屏幕高度和宽度自动计算最佳每页显示数量
- **响应式卡片**: 使用固定宽高比，确保在不同屏幕上的一致性
- **紧凑布局**: 减少间距和边距，最大化内容显示区域

### 3. 分页显示（优化版）
- 动态计算每页显示数量（基于屏幕尺寸）
- 智能分页导航，最多显示5个页码（避免横向溢出）
- 支持上一页/下一页快速导航
- 移除了"第X页，共X页"文字，使界面更简洁

### 4. 过滤功能（增强版）
- **全部**: 显示所有生成的车牌
- **有历史记录**: 只显示数据库中有查询记录的车牌
- **无历史记录**: 只显示从未查询过的车牌
- **可用**: 只显示历史记录中标记为可用的车牌
- **不可用**: 只显示历史记录中标记为不可用的车牌
- **Personalised**: 只显示个性化车牌类型
- **Custom**: 只显示自定义车牌类型

### 5. 排序功能
- **优先无历史记录**: 将未查询过的车牌排在前面
- **优先不可用**: 将历史记录为不可用的车牌排在前面
- **优先可用**: 将历史记录为可用的车牌排在前面
- **字母顺序**: 按车牌号码字母顺序排序

### 6. 选择功能
- 单击车牌进行选择/取消选择
- 选中的车牌会高亮显示并显示勾选图标
- 长按车牌复制到剪贴板
- 实时显示已选择数量
- 最多可选择100个车牌（可配置）

### 7. 批量选择功能（优化版）
- **当前页**: 一次性选择当前页面的所有车牌（精简文字）
- **无历史**: 选择所有没有查询历史的车牌（精简文字）
- **不可用**: 选择所有历史记录为不可用的车牌（精简文字）
- **清空**: 仅图标按钮，清除所有已选择的车牌
- **单行布局**: 所有按钮放在一行中，支持横向滚动
- **紧凑设计**: 减小图标尺寸，精简文字内容

### 8. 统计信息
- 显示总生成数量和过滤后数量
- 显示已选择数量和最大选择限制
- 当达到最大选择数量时会有视觉提示

### 9. 查询选项
- **查询选中的**: 只查询用户选择的车牌
- **查询全部**: 查询所有生成的车牌（保持原有功能）

## 界面改进

### 视觉效果（参考 history 页面）
- **紧凑卡片设计**: 使用更小的字体和间距，提高信息密度
- **动态网格布局**: 根据屏幕尺寸自动调整列数和卡片大小
- **选中状态高亮**: 选中的车牌使用主题色高亮显示
- **历史记录颜色区分**: 绿色=可用，红色=不可用
- **勾选图标**: 清晰的选中状态指示

### 用户体验
- **响应式设计**: 适配不同屏幕尺寸，自动优化布局
- **智能分页**: 避免横向溢出，最多显示5个页码
- **紧凑控件**: 批量选择按钮单行布局，支持横向滚动
- **简洁界面**: 移除冗余文字，保持界面清爽

## 性能优化

### 内存管理
- **动态分页**: 根据屏幕尺寸计算最佳每页数量，减少同时渲染的组件
- **高效数据结构**: 使用Set存储选择状态，提高查找效率
- **智能计算**: 避免不必要的过滤和排序计算

### 用户体验
- **大容量支持**: 最大生成数量从100提升到1000
- **流畅交互**: 分页加载，避免界面卡顿
- **高效操作**: 批量选择和紧凑控件提高操作效率
- **自适应布局**: 根据设备特性自动优化显示效果

## 使用方法

1. **生成车牌**: 输入关键词和位数，点击生成
2. **过滤和排序**: 使用下拉菜单选择过滤条件和排序方式
3. **选择车牌**: 
   - 单击车牌进行选择
   - 使用批量选择按钮快速选择
   - 长按复制车牌号码
4. **分页浏览**: 使用底部分页控件浏览所有车牌
5. **开始查询**: 
   - 选择特定车牌后点击"查询选中的"
   - 或点击"查询全部"查询所有车牌

## 技术实现

### 核心方法
- `_applyFilterAndSort()`: 应用过滤和排序逻辑
- `_updateCurrentPagePlates()`: 更新当前页显示内容
- `_togglePlateSelection()`: 切换车牌选择状态
- `_goToPage()`: 页面导航

### 状态管理
- 使用多个状态变量管理不同功能
- 合理的状态更新策略，避免不必要的重建
- 响应式UI更新

## 后续扩展

### 预留接口
- 排序功能预留了珍稀度排序的接口
- 配置常量便于后期调整
- 模块化设计便于添加新功能

### 可能的改进
- 添加搜索功能
- 支持自定义过滤条件
- 添加车牌珍稀度评估
- 支持导出选中的车牌
